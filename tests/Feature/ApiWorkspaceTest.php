<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Lara<PERSON>\Sanctum\Sanctum;
use Tests\TestCase;

class ApiWorkspaceTest extends TestCase
{
    use RefreshDatabase;

    public function test_api_workspaces_endpoint_works(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $response = $this->getJson('/api/workspaces');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data',
        ]);
    }
}
