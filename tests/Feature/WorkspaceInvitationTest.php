<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Workspace;
use App\Models\WorkspaceInvitation;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class WorkspaceInvitationTest extends TestCase
{
    use RefreshDatabase;

    public function test_workspace_owner_can_invite_user()
    {
        $owner = User::factory()->create();
        $workspace = Workspace::factory()->create(['user_id' => $owner->id]);

        $this->actingAs($owner)
            ->post(route('invitations.store', $workspace), [
                'email' => '<EMAIL>',
            ])
            ->assertRedirect();

        $this->assertDatabaseHas('workspace_invitations', [
            'workspace_id' => $workspace->id,
            'sender_id' => $owner->id,
            'email' => '<EMAIL>',
        ]);
    }

    public function test_user_can_accept_invitation()
    {
        $owner = User::factory()->create();
        $invitee = User::factory()->create();
        $workspace = Workspace::factory()->create(['user_id' => $owner->id]);

        $invitation = WorkspaceInvitation::create([
            'workspace_id' => $workspace->id,
            'sender_id' => $owner->id,
            'email' => $invitee->email,
            'token' => 'test-token',
        ]);

        $this->actingAs($invitee)
            ->post(route('workspace-invitations.accept', $invitation))
            ->assertRedirect();

        // Check that user was added to workspace
        $this->assertTrue($workspace->hasMember($invitee));

        // Check that invitation was deleted
        $this->assertDatabaseMissing('workspace_invitations', [
            'id' => $invitation->id,
        ]);
    }

    public function test_user_can_reject_invitation()
    {
        $owner = User::factory()->create();
        $invitee = User::factory()->create();
        $workspace = Workspace::factory()->create(['user_id' => $owner->id]);

        $invitation = WorkspaceInvitation::create([
            'workspace_id' => $workspace->id,
            'sender_id' => $owner->id,
            'email' => $invitee->email,
            'token' => 'test-token',
        ]);

        $this->actingAs($invitee)
            ->post(route('workspace-invitations.reject', $invitation))
            ->assertRedirect();

        // Check that user was not added to workspace
        $this->assertFalse($workspace->hasMember($invitee));

        // Check that invitation was deleted
        $this->assertDatabaseMissing('workspace_invitations', [
            'id' => $invitation->id,
        ]);
    }

    public function test_workspace_owner_can_cancel_invitation()
    {
        $owner = User::factory()->create();
        $workspace = Workspace::factory()->create(['user_id' => $owner->id]);

        $invitation = WorkspaceInvitation::create([
            'workspace_id' => $workspace->id,
            'sender_id' => $owner->id,
            'email' => '<EMAIL>',
            'token' => 'test-token',
        ]);

        $this->actingAs($owner)
            ->delete(route('invitations.destroy', [$workspace, $invitation]))
            ->assertRedirect();

        $this->assertDatabaseMissing('workspace_invitations', [
            'id' => $invitation->id,
        ]);
    }
}
