# Pod 文件管理器功能说明

## 概述

基于对现有 PodCommandExecutor 和 Pods/Index.vue 中命令执行机制的研究，我创建了一个完整的 Pod 文件管理页面。此页面允许用户通过 Web 界面管理 Kubernetes Pods 内的文件系统。

## 核心特性

### 1. 文件浏览和导航
- **文件列表展示**：支持列表和网格两种视图模式
- **路径导航**：面包屑导航，支持点击跳转到任意路径
- **文件类型识别**：区分文件、目录、软链接、块设备、字符设备、管道、Socket 等
- **权限显示**：显示文件权限、所有者、大小、修改时间等信息
- **文件搜索**：实时搜索过滤文件列表

### 2. 文件操作
- **预览文件**：支持文本文件和图片文件的预览
- **编辑文件**：内置文本编辑器，支持在线编辑文件内容
- **下载文件**：将 Pod 内文件下载到本地
- **上传文件**：将本地文件上传到 Pod 内
- **复制文件**：文件和目录的复制操作
- **移动/重命名**：支持文件和目录的重命名/移动
- **删除**：单个或批量删除文件/目录

### 3. 目录管理
- **新建目录**：创建新的文件夹
- **新建文件**：创建新文件，可选填入初始内容
- **快速导航**：快捷跳转到常用目录（主目录、根目录、临时目录）

### 4. 压缩和解压
- **智能压缩**：支持 ZIP、TAR、TAR.GZ 等格式的压缩
- **智能解压**：自动识别压缩格式并解压（ZIP、TAR、TAR.GZ、TAR.BZ2、TAR.XZ、RAR、7Z）
- **工具检测**：自动检测 Pod 内是否有相应的压缩工具

### 5. 安全机制
- **文件大小限制**：前端文件操作限制为 512KB，防止内存溢出
- **Base64 编码**：所有文件内容通过 Base64 编码传输，避免二进制问题
- **权限检查**：尊重文件系统权限
- **路径验证**：防止路径注入攻击

## 技术实现

### 后端实现
- **控制器**：`PodFileManagerController` 提供页面渲染
- **命令执行**：复用现有的 `PodTerminalService` 进行命令执行
- **DTO 支持**：基于 `PodCommandResultDTO` 处理命令结果
- **API 复用**：使用现有的 `/api/pods/{name}/exec` API 端点

### 前端实现
- **Vue 3 + TypeScript**：使用 Composition API
- **UI 组件**：基于项目现有的 UI 组件库
- **状态管理**：使用 Vue 3 的响应式系统
- **错误处理**：完整的错误处理和用户反馈

### 命令基础
通过执行以下 Linux 命令实现文件管理功能：
- `ls -la --time-style=full-iso` - 文件列表
- `mkdir -p` - 创建目录
- `rm -rf / rm -f` - 删除文件/目录
- `mv` - 移动/重命名
- `cp / cp -r` - 复制文件/目录
- `cat` - 读取文件内容
- `base64 / base64 -d` - 文件编码/解码
- `touch` - 创建空文件
- `df -h` - 磁盘使用情况
- 压缩工具：`zip`, `unzip`, `tar`, `7z` 等

## 用户界面

### 主要区域
1. **顶部工具栏**：Pod 选择器、容器选择器、刷新按钮
2. **文件浏览器**：主要的文件列表区域，支持多选
3. **操作面板**：侧边栏，显示选中文件信息和快速操作
4. **对话框**：文件预览、编辑、创建等操作的弹窗

### 交互设计
- **双击操作**：双击目录进入，双击文件预览
- **右键菜单**：通过下拉菜单进行文件操作
- **拖拽支持**：计划future版本支持
- **键盘快捷键**：计划future版本支持

## 访问方式

1. **侧边栏导航**：在"工作负载"部分新增"文件管理"菜单项
2. **Pod 操作菜单**：在 Pod 列表页面，每个 Pod 的操作菜单中新增"文件管理"选项
3. **直接访问**：`/pods-filemanager?pod={podName}` URL

## 路由配置
```php
// 在 routes/web.php 中添加
Route::get('pods-filemanager', [PodFileManagerController::class, 'index'])
    ->name('pods.filemanager');
```

## 安全注意事项

1. **文件大小限制**：防止大文件操作导致的资源消耗
2. **路径安全**：所有路径操作都经过验证
3. **权限继承**：操作受到 Pod 内文件系统权限限制
4. **命令注入防护**：参数经过适当转义
5. **Base64 传输**：避免特殊字符和二进制数据问题

## 兼容性

- **容器要求**：目标容器需要包含基本的 Unix 工具（ls, mkdir, rm, mv, cp, cat等）
- **权限要求**：执行用户需要对操作路径有相应权限
- **压缩工具**：压缩功能需要容器内安装相应工具

## Future 改进计划

1. **拖拽上传**：支持拖拽文件到浏览器直接上传
2. **大文件支持**：分块传输支持更大文件
3. **实时同步**：WebSocket 实时更新文件变化
4. **文件版本**：简单的文件版本管理
5. **权限管理**：更细粒度的权限控制
6. **搜索增强**：支持内容搜索和正则表达式
7. **文件预览增强**：支持更多文件格式的预览

## 开发测试建议

1. 使用包含常用工具的容器镜像进行测试（如 alpine, ubuntu 等）
2. 测试各种文件类型和大小
3. 验证权限边界情况
4. 测试网络异常情况下的用户体验
5. 确保在不同容器环境下的兼容性
