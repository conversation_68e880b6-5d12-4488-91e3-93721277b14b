import axios from '@/lib/axios'
import type { Pod, PodContainer } from '@/types'
import { ref, computed } from 'vue'
import { toast } from 'vue-sonner'

export interface FileItem {
    name: string
    type: 'file' | 'directory' | 'link' | 'block' | 'character' | 'pipe' | 'socket'
    size: number
    permissions: string
    modified: string
    owner?: string
    group?: string
    isExecutable?: boolean
    linkTarget?: string
}

export interface FileManagerState {
    selectedPod: string
    selectedContainer: string
    currentPath: string
    files: FileItem[]
    selectedFiles: string[]
    loading: boolean
}

export interface FileOperationResult {
    success: boolean
    stdout: string
    stderr: string
    error?: string
}

export function useFileManager(pods: Pod[]) {
    const state = ref<FileManagerState>({
        selectedPod: '',
        selectedContainer: '',
        currentPath: '/',
        files: [],
        selectedFiles: [],
        loading: false
    })

    const currentPodContainers = computed<PodContainer[]>(() => {
        if (!state.value.selectedPod) return []
        const pod = pods.find(p => p.name === state.value.selectedPod)
        return pod?.containers || []
    })

    const pathSegments = computed(() => {
        return state.value.currentPath.split('/').filter((segment, index) => index === 0 || segment !== '')
    })

    const hasSelection = computed(() => state.value.selectedFiles.length > 0)

    const canExtractSelected = computed(() => {
        return state.value.selectedFiles.some(fileName => {
            return /\.(zip|tar|tar\.gz|tgz|tar\.bz2|tbz2|tar\.xz|txz|rar|7z)$/i.test(fileName)
        })
    })

    const executeCommand = async (command: string[]): Promise<FileOperationResult> => {
        try {
            const response = await axios.post(`/api/pods/${state.value.selectedPod}/exec`, {
                command,
                container: state.value.selectedContainer,
                timeout: 30
            })
            
            const result = response.data
            return {
                success: result.success || (result.exit_code === 0) || (result.completed && !result.error),
                stdout: result.stdout || '',
                stderr: result.stderr || '',
                error: result.error
            }
        } catch (error: any) {
            return {
                success: false,
                stdout: '',
                stderr: '',
                error: error.response?.data?.message || error.message
            }
        }
    }

    const parseLsOutput = (output: string): FileItem[] => {
        const lines = output.split('\n').filter(line => line.trim())
        const items: FileItem[] = []
        
        for (const line of lines) {
            if (line.startsWith('total ') || line.trim() === '') continue
            
            try {
                const parts = line.split(/\s+/)
                if (parts.length < 9) continue
                
                const permissions = parts[0]
                const owner = parts[2]
                const group = parts[3]
                const size = parseInt(parts[4]) || 0
                const dateTime = `${parts[5]} ${parts[6]}`
                const name = parts.slice(8).join(' ')
                
                if (name === '.' || name === '..') continue
                
                let type: FileItem['type'] = 'file'
                let linkTarget: string | undefined
                
                if (permissions.startsWith('d')) {
                    type = 'directory'
                } else if (permissions.startsWith('l')) {
                    type = 'link'
                    const linkMatch = name.match(/^(.+) -> (.+)$/)
                    if (linkMatch) {
                        linkTarget = linkMatch[2]
                    }
                } else if (permissions.startsWith('b')) {
                    type = 'block'
                } else if (permissions.startsWith('c')) {
                    type = 'character'
                } else if (permissions.startsWith('p')) {
                    type = 'pipe'
                } else if (permissions.startsWith('s')) {
                    type = 'socket'
                }
                
                items.push({
                    name: linkTarget ? name.split(' -> ')[0] : name,
                    type,
                    size,
                    permissions,
                    modified: dateTime,
                    owner,
                    group,
                    isExecutable: permissions.includes('x'),
                    linkTarget
                })
            } catch (error) {
                console.warn('Failed to parse line:', line, error)
            }
        }
        
        return items.sort((a, b) => {
            if (a.type === 'directory' && b.type !== 'directory') return -1
            if (a.type !== 'directory' && b.type === 'directory') return 1
            return a.name.localeCompare(b.name)
        })
    }

    const refreshFileList = async () => {
        if (!state.value.selectedPod || !state.value.selectedContainer) return
        
        state.value.loading = true
        try {
            const response = await executeCommand(['ls', '-la', '--time-style=full-iso', state.value.currentPath])
            
            if (response.success) {
                state.value.files = parseLsOutput(response.stdout)
            } else {
                const errorMsg = response.stderr || response.error || 'Unknown error'
                toast.error(`无法列出文件: ${errorMsg}`)
            }
        } catch (error: any) {
            toast.error(`获取文件列表失败: ${error.message}`)
        } finally {
            state.value.loading = false
        }
    }

    const navigateToPath = (path: string) => {
        state.value.currentPath = path
        state.value.selectedFiles = []
        refreshFileList()
    }

    const navigateToFile = (file: FileItem) => {
        if (file.type === 'directory') {
            const newPath = state.value.currentPath === '/' 
                ? `/${file.name}`
                : `${state.value.currentPath}/${file.name}`
            navigateToPath(newPath)
        }
    }

    const toggleFileSelection = (fileName: string) => {
        const index = state.value.selectedFiles.indexOf(fileName)
        if (index === -1) {
            state.value.selectedFiles.push(fileName)
        } else {
            state.value.selectedFiles.splice(index, 1)
        }
    }

    const selectAll = () => {
        state.value.selectedFiles = [...state.value.files.map(f => f.name)]
    }

    const clearSelection = () => {
        state.value.selectedFiles = []
    }

    const getPathFromSegments = (index: number): string => {
        if (index === 0) return '/'
        const segments = pathSegments.value.slice(1, index + 1)
        return '/' + segments.join('/')
    }

    const setPod = (podName: string) => {
        state.value.selectedPod = podName
        state.value.selectedContainer = ''
        state.value.currentPath = '/'
        state.value.files = []
        state.value.selectedFiles = []
        
        const pod = pods.find(p => p.name === podName)
        if (pod && pod.containers.length > 0) {
            state.value.selectedContainer = pod.containers[0].name
            refreshFileList()
        }
    }

    const setContainer = (containerName: string) => {
        state.value.selectedContainer = containerName
        state.value.currentPath = '/'
        state.value.files = []
        state.value.selectedFiles = []
        if (containerName) {
            refreshFileList()
        }
    }

    return {
        state,
        currentPodContainers,
        pathSegments,
        hasSelection,
        canExtractSelected,
        executeCommand,
        refreshFileList,
        navigateToPath,
        navigateToFile,
        toggleFileSelection,
        selectAll,
        clearSelection,
        getPathFromSegments,
        setPod,
        setContainer
    }
}
