<template>
    <AppLayout>
        <Head title="文件管理器" />

        <template #header>
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-semibold text-gray-900 dark:text-gray-100">Pod 文件管理</h1>
                    <p class="mt-1 text-gray-600 dark:text-gray-400">管理 Kubernetes Pod 中的文件和目录</p>
                </div>
            </div>
        </template>

        <PodFileManager :pods="props.pods" />
    </AppLayout>
</template>

<script setup lang="ts">
import { Head } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import PodFileManager from '@/components/PodFileManager.vue'

interface Props {
    podName?: string
    pods: Pod[]
}

interface Pod {
    name: string
    containers: Array<{ name: string }>
}

const props = defineProps<Props>()
</script>
