export const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

export const getFileIcon = (file: { name: string; type: string }) => {
    if (file.type === 'directory') return 'lucide:folder'
    if (file.type === 'link') return 'lucide:link'
    if (file.type === 'block' || file.type === 'character') return 'lucide:hard-drive'
    if (file.type === 'pipe') return 'lucide:pipe'
    if (file.type === 'socket') return 'lucide:wifi'
    
    const ext = file.name.split('.').pop()?.toLowerCase()
    
    switch (ext) {
        case 'txt': case 'md': case 'readme':
            return 'lucide:file-text'
        case 'js': case 'ts': case 'jsx': case 'tsx':
            return 'lucide:code'
        case 'json':
            return 'lucide:braces'
        case 'html': case 'htm':
            return 'lucide:globe'
        case 'css': case 'scss': case 'sass':
            return 'lucide:palette'
        case 'img': case 'jpg': case 'jpeg': case 'png': case 'gif': case 'svg': case 'webp':
            return 'lucide:image'
        case 'mp3': case 'wav': case 'flac': case 'ogg':
            return 'lucide:music'
        case 'mp4': case 'avi': case 'mkv': case 'mov':
            return 'lucide:video'
        case 'pdf':
            return 'lucide:file-type'
        case 'zip': case 'rar': case 'tar': case 'gz': case '7z':
            return 'lucide:archive'
        case 'sh': case 'bash': case 'zsh':
            return 'lucide:terminal'
        case 'py':
            return 'lucide:snake'
        case 'php':
            return 'lucide:code'
        case 'docker': case 'dockerfile':
            return 'lucide:container'
        default:
            return 'lucide:file'
    }
}

export const isTextFile = (fileName: string): boolean => {
    const textExtensions = [
        'txt', 'md', 'readme', 'json', 'js', 'ts', 'jsx', 'tsx',
        'html', 'htm', 'css', 'scss', 'sass', 'xml', 'yaml', 'yml',
        'sh', 'bash', 'zsh', 'py', 'php', 'go', 'rs', 'cpp', 'c', 'h',
        'java', 'kt', 'swift', 'rb', 'pl', 'lua', 'sql', 'log', 'conf',
        'config', 'ini', 'env', 'gitignore', 'dockerfile', 'makefile'
    ]
    
    const ext = fileName.split('.').pop()?.toLowerCase()
    return textExtensions.includes(ext || '')
}

export const isArchiveFile = (fileName: string): boolean => {
    const archiveExtensions = ['zip', 'tar', 'tar.gz', 'tgz', 'tar.bz2', 'tbz2', 'tar.xz', 'txz', 'rar', '7z']
    return archiveExtensions.some(ext => fileName.toLowerCase().endsWith(ext))
}

export const getCompressionCommands = (files: string[], outputName: string, format: string) => {
    const filesList = files.map(f => `'${f.replace(/'/g, "'\"'\"'")}'`).join(' ')
    
    switch (format) {
        case 'zip':
            return ['zip', '-r', `${outputName}.zip`, ...files]
        case 'tar':
            return ['tar', '-czf', `${outputName}.tar.gz`, ...files]
        case 'tar.bz2':
            return ['tar', '-cjf', `${outputName}.tar.bz2`, ...files]
        case 'tar.xz':
            return ['tar', '-cJf', `${outputName}.tar.xz`, ...files]
        default:
            return ['tar', '-czf', `${outputName}.tar.gz`, ...files]
    }
}

export const getExtractionCommands = (fileName: string, outputDir?: string) => {
    const targetDir = outputDir || '.'
    
    if (fileName.endsWith('.zip')) {
        return ['unzip', fileName, '-d', targetDir]
    } else if (fileName.endsWith('.tar.gz') || fileName.endsWith('.tgz')) {
        return ['tar', '-xzf', fileName, '-C', targetDir]
    } else if (fileName.endsWith('.tar.bz2') || fileName.endsWith('.tbz2')) {
        return ['tar', '-xjf', fileName, '-C', targetDir]
    } else if (fileName.endsWith('.tar.xz') || fileName.endsWith('.txz')) {
        return ['tar', '-xJf', fileName, '-C', targetDir]
    } else if (fileName.endsWith('.tar')) {
        return ['tar', '-xf', fileName, '-C', targetDir]
    } else if (fileName.endsWith('.rar')) {
        return ['unrar', 'x', fileName, targetDir]
    } else if (fileName.endsWith('.7z')) {
        return ['7z', 'x', fileName, `-o${targetDir}`]
    }
    
    return ['tar', '-xzf', fileName, '-C', targetDir]
}

export const escapeShellArg = (arg: string): string => {
    return `'${arg.replace(/'/g, "'\"'\"'")}'`
}
