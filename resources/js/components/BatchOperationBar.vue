<template>
  <div
    v-if="selectedCount > 0"
    class="flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg dark:bg-blue-900/20 dark:border-blue-800"
  >
    <div class="flex items-center gap-3">
      <Icon name="lucide:check-square" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
      <span class="text-sm font-medium text-blue-900 dark:text-blue-100">
        已选择 {{ selectedCount }} 项
      </span>
      <Button
        variant="ghost"
        size="sm"
        @click="$emit('clear-selection')"
        class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
      >
        清除选择
      </Button>
    </div>

    <div class="flex items-center gap-2">
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <Button variant="outline" size="sm">
            <Icon name="lucide:download" class="w-4 h-4 mr-2" />
            下载
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuItem @click="$emit('download-selected')">
            <Icon name="lucide:download" class="mr-2 h-4 w-4" />
            下载选中项
          </DropdownMenuItem>
          <DropdownMenuItem @click="$emit('download-as-archive')">
            <Icon name="lucide:archive" class="mr-2 h-4 w-4" />
            打包下载
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <Button variant="outline" size="sm">
            <Icon name="lucide:archive" class="w-4 h-4 mr-2" />
            压缩
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuItem @click="$emit('compress', 'zip')">
            <Icon name="lucide:file-archive" class="mr-2 h-4 w-4" />
            压缩为 ZIP
          </DropdownMenuItem>
          <DropdownMenuItem @click="$emit('compress', 'tar')">
            <Icon name="lucide:file-archive" class="mr-2 h-4 w-4" />
            压缩为 TAR.GZ
          </DropdownMenuItem>
          <DropdownMenuItem @click="$emit('compress', 'tar.bz2')">
            <Icon name="lucide:file-archive" class="mr-2 h-4 w-4" />
            压缩为 TAR.BZ2
          </DropdownMenuItem>
          <DropdownMenuItem @click="$emit('compress', 'tar.xz')">
            <Icon name="lucide:file-archive" class="mr-2 h-4 w-4" />
            压缩为 TAR.XZ
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <Button
        variant="outline"
        size="sm"
        @click="$emit('copy-selected')"
      >
        <Icon name="lucide:copy" class="w-4 h-4 mr-2" />
        复制
      </Button>

      <Button
        variant="outline"
        size="sm"
        @click="$emit('move-selected')"
      >
        <Icon name="lucide:move" class="w-4 h-4 mr-2" />
        移动
      </Button>

      <Button
        variant="destructive"
        size="sm"
        @click="$emit('delete-selected')"
      >
        <Icon name="lucide:trash-2" class="w-4 h-4 mr-2" />
        删除
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  selectedCount: number
}

interface Emits {
  (e: 'clear-selection'): void
  (e: 'download-selected'): void
  (e: 'download-as-archive'): void
  (e: 'compress', format: string): void
  (e: 'copy-selected'): void
  (e: 'move-selected'): void
  (e: 'delete-selected'): void
}

defineProps<Props>()
defineEmits<Emits>()
</script>
