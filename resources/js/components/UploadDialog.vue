<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent>
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <Icon name="lucide:upload" class="w-5 h-5" />
          上传文件
        </DialogTitle>
      </DialogHeader>
      
      <div class="mt-4 space-y-4">
        <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
          <Icon name="lucide:upload-cloud" class="w-12 h-12 mx-auto text-gray-400 mb-4" />
          <input
            type="file"
            ref="fileInput"
            @change="handleFileSelect"
            multiple
            class="hidden"
          />
          <Button @click="fileInput?.click()">
            选择文件
          </Button>
          <p class="mt-2 text-sm text-gray-500">或将文件拖放到此处</p>
          <p class="mt-1 text-xs text-gray-400">最大文件大小: 512KB</p>
        </div>
        
        <div v-if="selectedFiles.length > 0" class="space-y-2">
          <div
            v-for="(file, index) in selectedFiles"
            :key="index"
            class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded"
          >
            <span class="text-sm truncate">{{ file.name }}</span>
            <Button
              variant="ghost"
              size="sm"
              @click="removeFile(index)"
            >
              <Icon name="lucide:x" class="w-4 h-4" />
            </Button>
          </div>
        </div>
        
        <div class="flex gap-2">
          <Button @click="handleUpload" :disabled="selectedFiles.length === 0 || uploading">
            <Icon name="lucide:upload" class="w-4 h-4 mr-2" />
            {{ uploading ? '上传中...' : '上传' }}
          </Button>
          <Button variant="outline" @click="$emit('update:open', false)">
            取消
          </Button>
        </div>
      </div>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface Props {
  open: boolean
}

interface Emits {
  (e: 'update:open', value: boolean): void
  (e: 'upload', files: File[]): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

const fileInput = ref<HTMLInputElement>()
const selectedFiles = ref<File[]>([])
const uploading = ref(false)

const handleFileSelect = (event: Event) => {
  const input = event.target as HTMLInputElement
  if (input.files) {
    selectedFiles.value = [...selectedFiles.value, ...Array.from(input.files)]
  }
}

const removeFile = (index: number) => {
  selectedFiles.value.splice(index, 1)
}

const handleUpload = () => {
  if (selectedFiles.value.length === 0) return
  
  uploading.value = true
  emit('upload', selectedFiles.value)
  
  // Reset
  selectedFiles.value = []
  uploading.value = false
  emit('update:open', false)
}
</script>
