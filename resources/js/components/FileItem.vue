<template>
  <div
    :class="[
      'group flex items-center gap-3 p-3 rounded-md transition-all duration-200 cursor-pointer border',
      'hover:shadow-sm',
      {
        'bg-blue-50 border-blue-200 dark:bg-blue-900/30 dark:border-blue-700': isSelected,
        'bg-white border-gray-200 hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-700 dark:hover:bg-gray-700': !isSelected
      }
    ]"
    @click="handleClick"
    @contextmenu.prevent="showContextMenu = true"
  >
    <!-- 选择复选框 -->
    <div class="flex-shrink-0">
      <input
        type="checkbox"
        :checked="isSelected"
        @change="$emit('toggle')"
        @click.stop
        class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
      >
    </div>

    <!-- 文件图标 -->
    <div class="flex-shrink-0">
      <Icon 
        :name="getFileIcon(file)" 
        :class="[
          'w-5 h-5',
          {
            'text-blue-500': file.type === 'directory',
            'text-green-500': file.type === 'link',
            'text-orange-500': file.isExecutable,
            'text-gray-500 dark:text-gray-400': file.type === 'file' && !file.isExecutable
          }
        ]"
      />
    </div>

    <!-- 文件信息 -->
    <div class="flex-1 min-w-0">
      <div class="flex items-center gap-2">
        <span class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
          {{ file.name }}
        </span>
        <span
          v-if="file.linkTarget"
          class="text-xs text-gray-500 dark:text-gray-400 truncate"
        >
          → {{ file.linkTarget }}
        </span>
      </div>
      <div class="flex items-center gap-4 mt-1 text-xs text-gray-500 dark:text-gray-400">
        <span>{{ formatFileSize(file.size) }}</span>
        <span>{{ file.permissions }}</span>
        <span v-if="file.owner">{{ file.owner }}</span>
        <span>{{ file.modified }}</span>
      </div>
    </div>

    <!-- 操作菜单按钮 -->
    <div class="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity">
      <DropdownMenu :open="showContextMenu" @update:open="showContextMenu = $event">
        <DropdownMenuTrigger as-child>
          <Button variant="ghost" size="sm" class="h-8 w-8 p-0">
            <Icon name="lucide:more-horizontal" class="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" class="w-56">
          <DropdownMenuItem
            v-if="file.type === 'directory'"
            @click="$emit('enter')"
          >
            <Icon name="lucide:folder-open" class="mr-2 h-4 w-4" />
            打开目录
          </DropdownMenuItem>
          <DropdownMenuItem
            v-if="file.type === 'file' && isTextFile(file.name)"
            @click="$emit('preview')"
          >
            <Icon name="lucide:eye" class="mr-2 h-4 w-4" />
            预览
          </DropdownMenuItem>
          <DropdownMenuItem
            v-if="file.type === 'file' && isTextFile(file.name)"
            @click="$emit('edit')"
          >
            <Icon name="lucide:edit" class="mr-2 h-4 w-4" />
            编辑
          </DropdownMenuItem>
          <DropdownMenuItem
            v-if="file.type === 'file'"
            @click="$emit('download')"
          >
            <Icon name="lucide:download" class="mr-2 h-4 w-4" />
            下载
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem @click="$emit('rename')">
            <Icon name="lucide:edit-2" class="mr-2 h-4 w-4" />
            重命名
          </DropdownMenuItem>
          <DropdownMenuItem @click="$emit('copy')">
            <Icon name="lucide:copy" class="mr-2 h-4 w-4" />
            复制
          </DropdownMenuItem>
          <DropdownMenuItem @click="$emit('move')">
            <Icon name="lucide:move" class="mr-2 h-4 w-4" />
            移动
          </DropdownMenuItem>
          <DropdownMenuSeparator v-if="isArchiveFile(file.name)" />
          <DropdownMenuItem
            v-if="isArchiveFile(file.name)"
            @click="$emit('extract')"
          >
            <Icon name="lucide:archive" class="mr-2 h-4 w-4" />
            解压缩
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            @click="$emit('delete')"
            class="text-red-600 dark:text-red-400"
          >
            <Icon name="lucide:trash-2" class="mr-2 h-4 w-4" />
            删除
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { FileItem } from '@/composables/useFileManager'
import { getFileIcon, formatFileSize, isTextFile, isArchiveFile } from '@/lib/fileUtils'

interface Props {
  file: FileItem
  isSelected: boolean
}

interface Emits {
  (e: 'click'): void
  (e: 'toggle'): void
  (e: 'enter'): void
  (e: 'preview'): void
  (e: 'edit'): void
  (e: 'download'): void
  (e: 'rename'): void
  (e: 'copy'): void
  (e: 'move'): void
  (e: 'delete'): void
  (e: 'extract'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const showContextMenu = ref(false)

const handleClick = () => {
  // 单击直接进入目录或选择文件
  emit('click')
}
</script>
