<template>
  <div class="space-y-6">
    <!-- Pod 和容器选择 -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div class="space-y-2">
        <Label>选择 Pod</Label>
        <Select v-model="fileManager.state.value.selectedPod" @update:modelValue="fileManager.setPod">
          <SelectTrigger>
            <SelectValue placeholder="请选择 Pod" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem
              v-for="pod in pods"
              :key="pod.name"
              :value="pod.name"
            >
              {{ pod.name }}
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div class="space-y-2">
        <Label>选择容器</Label>
        <Select 
          v-model="fileManager.state.value.selectedContainer" 
          @update:modelValue="fileManager.setContainer"
          :disabled="!fileManager.state.value.selectedPod"
        >
          <SelectTrigger>
            <SelectValue placeholder="请选择容器" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem
              v-for="container in fileManager.currentPodContainers.value"
              :key="container.name"
              :value="container.name"
            >
              {{ container.name }}
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>

    <!-- 文件管理器主体 -->
    <div v-if="fileManager.state.value.selectedPod && fileManager.state.value.selectedContainer" class="space-y-4">
      <!-- 路径导航 -->
      <div class="flex items-center gap-2 p-3 bg-gray-50 rounded-lg dark:bg-gray-800">
        <Icon name="lucide:folder" class="w-4 h-4 text-gray-500" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem
              v-for="(segment, index) in fileManager.pathSegments.value"
              :key="index"
            >
              <BreadcrumbLink
                @click="fileManager.navigateToPath(fileManager.getPathFromSegments(index))"
                class="cursor-pointer hover:text-blue-600"
              >
                {{ index === 0 ? '根目录' : segment }}
              </BreadcrumbLink>
              <BreadcrumbSeparator v-if="index < fileManager.pathSegments.value.length - 1" />
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        
        <div class="ml-auto flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            @click="fileManager.refreshFileList"
            :disabled="fileManager.state.value.loading"
          >
            <Icon name="lucide:refresh-cw" :class="['w-4 h-4', { 'animate-spin': fileManager.state.value.loading }]" />
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            @click="showCreateDialog = true"
          >
            <Icon name="lucide:plus" class="w-4 h-4 mr-2" />
            新建
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            @click="showUploadDialog = true"
          >
            <Icon name="lucide:upload" class="w-4 h-4 mr-2" />
            上传
          </Button>
        </div>
      </div>

      <!-- 批量操作工具栏 -->
      <BatchOperationBar
        :selected-count="fileManager.state.value.selectedFiles.length"
        @clear-selection="fileManager.clearSelection"
        @download-selected="downloadSelected"
        @download-as-archive="downloadAsArchive"
        @compress="compressSelected"
        @copy-selected="copySelected"
        @move-selected="moveSelected"
        @delete-selected="deleteSelected"
      />

      <!-- 文件列表 -->
      <div class="space-y-2">
        <div v-if="fileManager.state.value.loading" class="text-center py-8">
          <Icon name="lucide:loader-2" class="w-8 h-8 animate-spin mx-auto text-gray-400" />
          <p class="mt-2 text-gray-500">加载中...</p>
        </div>
        
        <div v-else-if="fileManager.state.value.files.length === 0" class="text-center py-8">
          <Icon name="lucide:folder-open" class="w-12 h-12 mx-auto text-gray-300 dark:text-gray-600" />
          <p class="mt-2 text-gray-500">此目录为空</p>
        </div>
        
        <div v-else class="grid gap-2">
          <div class="flex items-center gap-3 mb-2">
            <input
              type="checkbox"
              :checked="fileManager.state.value.selectedFiles.length === fileManager.state.value.files.length"
              @change="handleSelectAll"
              class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
            >
            <span class="text-sm text-gray-600 dark:text-gray-400">全选</span>
          </div>
          
          <FileItem
            v-for="file in fileManager.state.value.files"
            :key="file.name"
            :file="file"
            :is-selected="fileManager.state.value.selectedFiles.includes(file.name)"
            @click="handleFileClick(file)"
            @toggle="fileManager.toggleFileSelection(file.name)"
            @enter="fileManager.navigateToFile(file)"
            @preview="previewFile(file)"
            @edit="editFile(file)"
            @download="downloadFile(file)"
            @rename="renameFile(file)"
            @copy="copyFile(file)"
            @move="moveFile(file)"
            @delete="deleteFile(file)"
            @extract="extractFile(file)"
          />
        </div>
      </div>
    </div>

    <!-- 对话框 -->
    <FilePreviewDialog
      v-model:open="showPreviewDialog"
      :file="currentFile"
      :content="fileContent"
      :loading="loadingContent"
    />
    
    <FileEditDialog
      v-model:open="showEditDialog"
      :file="currentFile"
      :content="fileContent"
      :loading="loadingContent"
      @save="saveFile"
    />
    
    <CreateItemDialog
      v-model:open="showCreateDialog"
      @create="createItem"
    />
    
    <UploadDialog
      v-model:open="showUploadDialog"
      @upload="uploadFile"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useFileManager, type FileItem as FileItemType } from '@/composables/useFileManager'
import { isTextFile } from '@/lib/fileUtils'
import { useResourcesStore } from '@/stores/resources'
import { toast } from 'vue-sonner'

import FileItem from './FileItem.vue'
import BatchOperationBar from './BatchOperationBar.vue'
import FilePreviewDialog from './FilePreviewDialog.vue'
import FileEditDialog from './FileEditDialog.vue'
import CreateItemDialog from './CreateItemDialog.vue'
import UploadDialog from './UploadDialog.vue'

const resourcesStore = useResourcesStore()
const pods = computed(() => resourcesStore.pods || [])

// 初始化文件管理器
const fileManager = useFileManager(pods.value)

// 对话框状态
const showPreviewDialog = ref(false)
const showEditDialog = ref(false)
const showCreateDialog = ref(false)
const showUploadDialog = ref(false)

const currentFile = ref<FileItemType | null>(null)
const fileContent = ref('')
const loadingContent = ref(false)

const handleSelectAll = () => {
  if (fileManager.state.value.selectedFiles.length === fileManager.state.value.files.length) {
    fileManager.clearSelection()
  } else {
    fileManager.selectAll()
  }
}

const handleFileClick = (file: FileItemType) => {
  if (file.type === 'directory') {
    // 目录：单击进入
    fileManager.navigateToFile(file)
  } else {
    // 文件：单击选择/取消选择
    fileManager.toggleFileSelection(file.name)
  }
}

const previewFile = async (file: FileItemType) => {
  if (!isTextFile(file.name)) {
    toast.error('此文件类型不支持预览')
    return
  }

  currentFile.value = file
  loadingContent.value = true
  showPreviewDialog.value = true

  try {
    const filePath = fileManager.state.value.currentPath === '/' 
      ? `/${file.name}`
      : `${fileManager.state.value.currentPath}/${file.name}`
    
    const result = await fileManager.executeCommand(['cat', filePath])
    
    if (result.success) {
      fileContent.value = result.stdout
    } else {
      toast.error(`预览文件失败: ${result.stderr || result.error || '未知错误'}`)
      showPreviewDialog.value = false
    }
  } catch (error: any) {
    toast.error(`预览文件失败: ${error.message}`)
    showPreviewDialog.value = false
  } finally {
    loadingContent.value = false
  }
}

const editFile = async (file: FileItemType) => {
  if (!isTextFile(file.name)) {
    toast.error('此文件类型不支持编辑')
    return
  }

  currentFile.value = file
  loadingContent.value = true
  showEditDialog.value = true

  try {
    const filePath = fileManager.state.value.currentPath === '/' 
      ? `/${file.name}`
      : `${fileManager.state.value.currentPath}/${file.name}`
    
    const result = await fileManager.executeCommand(['cat', filePath])
    
    if (result.success) {
      fileContent.value = result.stdout
    } else {
      toast.error(`读取文件失败: ${result.stderr || result.error || '未知错误'}`)
      showEditDialog.value = false
    }
  } catch (error: any) {
    toast.error(`读取文件失败: ${error.message}`)
    showEditDialog.value = false
  } finally {
    loadingContent.value = false
  }
}

const saveFile = async (content: string) => {
  if (!currentFile.value) return

  try {
    const filePath = fileManager.state.value.currentPath === '/' 
      ? `/${currentFile.value.name}`
      : `${fileManager.state.value.currentPath}/${currentFile.value.name}`
    
    // 使用 base64 编码确保内容安全传输
    const encodedContent = btoa(unescape(encodeURIComponent(content)))
    const result = await fileManager.executeCommand([
      'bash', '-c', 
      `echo '${encodedContent}' | base64 -d > '${filePath.replace(/'/g, "'\"'\"'")}'`
    ])
    
    if (result.success) {
      toast.success('文件保存成功')
      showEditDialog.value = false
      fileManager.refreshFileList()
    } else {
      toast.error(`保存文件失败: ${result.stderr || result.error || '未知错误'}`)
    }
  } catch (error: any) {
    toast.error(`保存文件失败: ${error.message}`)
  }
}

const downloadFile = async (file: FileItemType) => {
  try {
    const filePath = fileManager.state.value.currentPath === '/' 
      ? `/${file.name}`
      : `${fileManager.state.value.currentPath}/${file.name}`
    
    const result = await fileManager.executeCommand(['base64', '-w', '0', filePath])
    
    if (result.success) {
      const binaryString = atob(result.stdout)
      const bytes = new Uint8Array(binaryString.length)
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i)
      }
      
      const blob = new Blob([bytes])
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = file.name
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      
      toast.success('文件下载成功')
    } else {
      toast.error(`下载文件失败: ${result.stderr || result.error || '未知错误'}`)
    }
  } catch (error: any) {
    toast.error(`下载文件失败: ${error.message}`)
  }
}

// 其他操作方法的实现...
const renameFile = async (file: FileItemType) => {
  // TODO: 实现重命名
}

const copyFile = async (file: FileItemType) => {
  // TODO: 实现复制
}

const moveFile = async (file: FileItemType) => {
  // TODO: 实现移动
}

const deleteFile = async (file: FileItemType) => {
  if (!confirm(`确认删除 ${file.name}？此操作不可撤销。`)) return
  
  try {
    const filePath = fileManager.state.value.currentPath === '/' 
      ? `/${file.name}`
      : `${fileManager.state.value.currentPath}/${file.name}`
    
    const command = file.type === 'directory' 
      ? ['rm', '-rf', filePath]
      : ['rm', '-f', filePath]
    
    const result = await fileManager.executeCommand(command)
    
    if (result.success) {
      toast.success('删除成功')
      fileManager.refreshFileList()
    } else {
      toast.error(`删除失败: ${result.stderr || result.error || '未知错误'}`)
    }
  } catch (error: any) {
    toast.error(`删除失败: ${error.message}`)
  }
}

const extractFile = async (file: FileItemType) => {
  // TODO: 实现解压缩
}

const downloadSelected = () => {
  // TODO: 实现批量下载
}

const downloadAsArchive = () => {
  // TODO: 实现打包下载
}

const compressSelected = (format: string) => {
  // TODO: 实现批量压缩
}

const copySelected = () => {
  // TODO: 实现批量复制
}

const moveSelected = () => {
  // TODO: 实现批量移动
}

const deleteSelected = async () => {
  if (fileManager.state.value.selectedFiles.length === 0) return
  
  if (!confirm(`确认删除 ${fileManager.state.value.selectedFiles.length} 个项目？此操作不可撤销。`)) return
  
  for (const fileName of fileManager.state.value.selectedFiles) {
    try {
      const filePath = fileManager.state.value.currentPath === '/' 
        ? `/${fileName}`
        : `${fileManager.state.value.currentPath}/${fileName}`
      
      const result = await fileManager.executeCommand(['rm', '-rf', filePath])
      
      if (!result.success) {
        toast.error(`删除 ${fileName} 失败: ${result.stderr || result.error || '未知错误'}`)
      }
    } catch (error: any) {
      toast.error(`删除 ${fileName} 失败: ${error.message}`)
    }
  }
  
  toast.success('批量删除完成')
  fileManager.clearSelection()
  fileManager.refreshFileList()
}

const createItem = async (data: { type: string; name: string }) => {
  try {
    const itemPath = fileManager.state.value.currentPath === '/' 
      ? `/${data.name}`
      : `${fileManager.state.value.currentPath}/${data.name}`
    
    let command: string[]
    if (data.type === 'directory') {
      command = ['mkdir', '-p', itemPath]
    } else {
      command = ['touch', itemPath]
    }
    
    const result = await fileManager.executeCommand(command)
    
    if (result.success) {
      toast.success(`${data.type === 'directory' ? '目录' : '文件'}创建成功`)
      fileManager.refreshFileList()
    } else {
      toast.error(`创建失败: ${result.stderr || result.error || '未知错误'}`)
    }
  } catch (error: any) {
    toast.error(`创建失败: ${error.message}`)
  }
}

const uploadFile = async (files: File[]) => {
  for (const file of files) {
    try {
      // 检查文件大小 (512KB限制)
      if (file.size > 512 * 1024) {
        toast.error(`文件 ${file.name} 超过 512KB 限制`)
        continue
      }
      
      // 读取文件内容并转换为 base64
      const reader = new FileReader()
      const fileContent = await new Promise<string>((resolve, reject) => {
        reader.onload = () => resolve(reader.result as string)
        reader.onerror = reject
        reader.readAsDataURL(file)
      })
      
      // 提取 base64 数据 (移除 data:mime;base64, 前缀)
      const base64Data = fileContent.split(',')[1]
      
      const filePath = fileManager.state.value.currentPath === '/' 
        ? `/${file.name}`
        : `${fileManager.state.value.currentPath}/${file.name}`
      
      const result = await fileManager.executeCommand([
        'bash', '-c',
        `echo '${base64Data}' | base64 -d > '${filePath.replace(/'/g, "'\"'\"'")}'`
      ])
      
      if (result.success) {
        toast.success(`文件 ${file.name} 上传成功`)
      } else {
        toast.error(`上传 ${file.name} 失败: ${result.stderr || result.error || '未知错误'}`)
      }
    } catch (error: any) {
      toast.error(`上传 ${file.name} 失败: ${error.message}`)
    }
  }
  
  fileManager.refreshFileList()
}

onMounted(async () => {
  await resourcesStore.loadPods()
})
</script>
