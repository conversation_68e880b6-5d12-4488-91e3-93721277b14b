<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="max-w-4xl max-h-[80vh]">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <Icon name="lucide:eye" class="w-5 h-5" />
          预览文件: {{ file?.name }}
        </DialogTitle>
      </DialogHeader>
      
      <div class="mt-4 max-h-96 overflow-auto">
        <div v-if="loading" class="text-center py-8">
          <Icon name="lucide:loader-2" class="w-6 h-6 animate-spin mx-auto text-gray-400" />
          <p class="mt-2 text-gray-500">加载中...</p>
        </div>
        <pre v-else class="text-sm whitespace-pre-wrap font-mono bg-gray-50 dark:bg-gray-800 p-4 rounded border overflow-x-auto">{{ content }}</pre>
      </div>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import type { FileItem } from '@/composables/useFileManager'

interface Props {
  open: boolean
  file: FileItem | null
  content: string
  loading: boolean
}

interface Emits {
  (e: 'update:open', value: boolean): void
}

defineProps<Props>()
defineEmits<Emits>()
</script>
