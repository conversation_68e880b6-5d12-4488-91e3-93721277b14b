<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="max-w-4xl max-h-[80vh]">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <Icon name="lucide:edit" class="w-5 h-5" />
          编辑文件: {{ file?.name }}
        </DialogTitle>
      </DialogHeader>
      
      <div class="mt-4 space-y-4">
        <div v-if="loading" class="text-center py-8">
          <Icon name="lucide:loader-2" class="w-6 h-6 animate-spin mx-auto text-gray-400" />
          <p class="mt-2 text-gray-500">加载中...</p>
        </div>
        <div v-else class="space-y-4">
          <Textarea
            v-model="editableContent"
            placeholder="文件内容"
            class="min-h-64 font-mono text-sm"
          />
          <div class="flex gap-2">
            <Button @click="handleSave" :disabled="loading">
              <Icon name="lucide:save" class="w-4 h-4 mr-2" />
              保存
            </Button>
            <Button variant="outline" @click="$emit('update:open', false)">
              取消
            </Button>
          </div>
        </div>
      </div>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import type { FileItem } from '@/composables/useFileManager'

interface Props {
  open: boolean
  file: FileItem | null
  content: string
  loading: boolean
}

interface Emits {
  (e: 'update:open', value: boolean): void
  (e: 'save', content: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const editableContent = ref('')

watch(() => props.content, (newContent) => {
  editableContent.value = newContent
}, { immediate: true })

const handleSave = () => {
  emit('save', editableContent.value)
}
</script>
