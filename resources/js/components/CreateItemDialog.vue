<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent>
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <Icon name="lucide:plus" class="w-5 h-5" />
          创建文件或目录
        </DialogTitle>
      </DialogHeader>
      
      <div class="mt-4 space-y-4">
        <div>
          <Label>类型</Label>
          <Select v-model="createType">
            <SelectTrigger>
              <SelectValue placeholder="选择类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="file">文件</SelectItem>
              <SelectItem value="directory">目录</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <Label>名称</Label>
          <Input v-model="createName" placeholder="输入名称" @keyup.enter="handleCreate" />
        </div>
        
        <div class="flex gap-2">
          <Button @click="handleCreate" :disabled="!createType || !createName">
            创建
          </Button>
          <Button variant="outline" @click="$emit('update:open', false)">
            取消
          </Button>
        </div>
      </div>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface Props {
  open: boolean
}

interface Emits {
  (e: 'update:open', value: boolean): void
  (e: 'create', data: { type: string; name: string }): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

const createType = ref('')
const createName = ref('')

const handleCreate = () => {
  if (!createType.value || !createName.value) return
  
  emit('create', {
    type: createType.value,
    name: createName.value
  })
  
  // Reset form
  createType.value = ''
  createName.value = ''
  emit('update:open', false)
}
</script>
