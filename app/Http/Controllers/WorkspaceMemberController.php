<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Workspace;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class WorkspaceMemberController extends Controller
{
    /**
     * 移除工作空间成员
     */
    public function destroy(Workspace $workspace, User $user)
    {
        $this->authorize('update', $workspace);

        // 不能移除工作空间所有者
        if ($workspace->user_id === $user->id) {
            return redirect()->back()->withErrors([
                'general' => '不能移除工作空间所有者',
            ]);
        }

        $workspace->removeMember($user);

        // 如果被移除的用户当前工作空间是这个，清除它
        if ($user->current_workspace_id === $workspace->id) {
            $user->update(['current_workspace_id' => null]);
        }

        return redirect()->back()->with('success', '成员已移除');
    }

    /**
     * 转让工作空间所有权
     */
    public function transferOwnership(Request $request, Workspace $workspace, User $user)
    {
        $this->authorize('update', $workspace);

        // 只有所有者可以转让所有权
        if ($workspace->user_id !== $request->user()->id) {
            abort(403, '只有工作空间所有者可以转让所有权');
        }

        // 新所有者必须是工作空间成员
        if (! $workspace->hasMember($user)) {
            return redirect()->back()->withErrors([
                'general' => '只能向工作空间成员转让所有权',
            ]);
        }

        DB::transaction(function () use ($workspace, $user) {
            $oldOwner = $workspace->user;

            // 更新工作空间所有者
            $workspace->update(['user_id' => $user->id]);

            // 将原所有者添加为普通成员（如果还不是的话）
            if ($oldOwner && ! $workspace->members()->where('user_id', $oldOwner->id)->exists()) {
                $workspace->addMember($oldOwner);
            }

            // 从成员列表中移除新所有者（避免重复）
            $workspace->removeMember($user);
        });

        return redirect()->back()->with('success', '工作空间所有权已转让');
    }
}
