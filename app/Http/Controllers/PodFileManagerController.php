<?php

namespace App\Http\Controllers;

use App\Service\PodService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class PodFileManagerController extends Controller
{
    /**
     * Display the file manager page
     */
    public function index(Request $request): Response
    {
        $workspace = $request->user()->getWorkspace();
        $podService = new PodService($workspace);

        $pods = $podService->getPods();

        // 获取 Pod 名称参数
        $podName = $request->query('pod');

        return Inertia::render('Pods/FileManager', [
            'podName' => $podName,
            'pods' => $pods,
        ]);
    }
}
