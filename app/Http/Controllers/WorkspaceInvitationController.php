<?php

namespace App\Http\Controllers;

use App\Http\Requests\InviteUserRequest;
use App\Http\Resources\WorkspaceInvitationResource;
use App\Models\User;
use App\Models\Workspace;
use App\Models\WorkspaceInvitation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Inertia\Inertia;

class WorkspaceInvitationController extends Controller
{
    /**
     * 邀请用户加入工作空间
     */
    public function store(InviteUserRequest $request, Workspace $workspace)
    {
        $this->authorize('create', [WorkspaceInvitation::class, $workspace]);

        $email = strtolower($request->validated()['email']);

        // 检查邮箱是否已经是工作空间所有者
        if ($workspace->user && $workspace->user->email === $email) {
            return redirect()->back()->withErrors([
                'email' => '该用户已经是工作空间所有者',
            ]);
        }

        $existingUser = User::where('email', $email)->first();
        if ($existingUser && $workspace->hasMember($existingUser)) {
            return redirect()->back()->withErrors([
                'email' => '该用户已经是工作空间成员',
            ]);
        }

        // 检查是否已有待处理的邀请
        $existingInvitation = $workspace->invitations()
            ->where('email', $email)
            ->first();

        if ($existingInvitation) {
            return redirect()->back()->withErrors([
                'email' => '该邮箱已有待处理的邀请',
            ]);
        }

        DB::transaction(function () use ($request, $workspace, $email, $existingUser) {
            $workspace->invitations()->create([
                'email' => $email,
                'sender_id' => $request->user()->id,
                'user_id' => $existingUser?->id,
                'token' => Str::random(32),
                'role' => $request->validated()['role'] ?? null,
            ]);

            // TODO: 发送邀请邮件
            // Mail::to($email)->send(new WorkspaceInvitationMail($invitation));
        });

        return redirect()->back()->with('success', '邀请已发送');
    }

    /**
     * 取消邀请
     */
    public function destroy(Workspace $workspace, WorkspaceInvitation $invitation)
    {
        $this->authorize('delete', $invitation);

        if ($invitation->workspace_id !== $workspace->id) {
            abort(404);
        }

        $invitation->delete();

        return redirect()->back()->with('success', '邀请已取消');
    }

    /**
     * 显示当前用户收到的邀请
     */
    public function myInvitations(Request $request)
    {
        $user = $request->user();

        $invitations = WorkspaceInvitation::where('email', $user->email)
            ->with(['workspace.user', 'sender'])
            ->orderBy('created_at', 'desc')
            ->get();

        return Inertia::render('Workspaces/MyInvitations', [
            'invitations' => WorkspaceInvitationResource::collection($invitations)->resolve(),
        ]);
    }

    /**
     * 接受邀请
     */
    public function accept(Request $request, WorkspaceInvitation $invitation)
    {
        $this->authorize('accept', $invitation);

        $user = $request->user();

        // 验证邮箱匹配
        if ($user->email !== $invitation->email) {
            abort(403, '邮箱不匹配');
        }

        DB::transaction(function () use ($invitation, $user) {
            $workspace = $invitation->workspace;

            // 添加用户到工作空间
            $workspace->addMember($user);

            // 如果用户没有 current_workspace_id
            if (! $user->current_workspace_id) {
                $user->setCurrentWorkspace($workspace);
            }

            // 删除邀请
            $invitation->delete();
        });

        return redirect()->route('workspaces.show', $invitation->workspace)
            ->with('success', '已加入工作空间');
    }

    /**
     * 拒绝邀请
     */
    public function reject(Request $request, WorkspaceInvitation $invitation)
    {
        $this->authorize('reject', $invitation);

        $user = $request->user();

        // 验证邮箱匹配
        if ($user->email !== $invitation->email) {
            abort(403, '邮箱不匹配');
        }

        $invitation->delete();

        return redirect()->back()->with('success', '已拒绝邀请');
    }
}
