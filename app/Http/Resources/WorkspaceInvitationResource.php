<?php

namespace App\Http\Resources;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WorkspaceInvitationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'workspace_id' => $this->workspace_id,
            'sender_id' => $this->sender_id,
            'email' => $this->email,
            'user_id' => $this->user_id,
            'token' => $this->token,
            'role' => $this->role,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'workspace' => $this->when($this->relationLoaded('workspace') && $this->resource->workspace, new WorkspaceResource($this->resource->workspace)->resolve()),
            'sender' => $this->when($this->relationLoaded('sender') && $this->resource->sender, new UserResource($this->resource->sender)->resolve()),
            'user' => $this->when($this->relationLoaded('user') && $this->resource->user, new UserResource($this->resource->user)->resolve()),
        ];
    }

    /**
     * Get additional data that should be merged with the resource array.
     */
    public function with(Request $request): array
    {
        return [];
    }

    /**
     * Customize the outgoing response for the resource.
     */
    public function withResponse(Request $request, JsonResponse $response): void
    {
        //
    }

    /**
     * Resolve the resource to an array.
     */
    public function resolve($request = null)
    {
        return $this->toArray($request ?: request());
    }
}
