<?php

namespace App\Service;

use App\Models\Workspace;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ImageValidationService
{
    private Client $httpClient;

    private Workspace $workspace;

    public function __construct(Workspace $workspace)
    {
        $this->workspace = $workspace;
        $this->httpClient = $this->createHttpClient();
    }

    /**
     * 验证镜像并获取大小信息
     *
     * @param  string  $image  完整的镜像名称
     * @param  array  $imagePullSecrets  镜像拉取密钥
     * @return array 返回验证结果和镜像信息
     */
    public function validateImageSize(string $image, array $imagePullSecrets = []): array
    {
        // 缓存 key 可包含 workspace id（如有）、image 名称和 imagePullSecrets（如有）
        $workspaceId = $this->workspace->id ?? 'default';
        $secretsKey = ! empty($imagePullSecrets) ? md5(json_encode($imagePullSecrets)) : 'nosecret';
        $cacheKey = "image_validation:{$workspaceId}:{$image}:{$secretsKey}";

        $cached = Cache::get($cacheKey);
        if ($cached !== null) {
            return $cached;
        }

        try {
            [$registry, $repository, $tag] = $this->parseImageName($image);

            $credentials = $this->getRegistryCredentials($registry, $imagePullSecrets);
            $token = $this->getAuthToken($registry, $repository, $credentials);
            $manifest = $this->getManifest($registry, $repository, $tag, $token);

            // 处理多架构镜像
            if (isset($manifest['manifests'])) {
                Log::info('检测到多架构镜像，选择 amd64 架构');
                $amd64Manifest = collect($manifest['manifests'])->firstWhere('platform.architecture', 'amd64');

                if (! $amd64Manifest) {
                    throw new \Exception('未找到 amd64 架构的镜像');
                }

                $digest = $amd64Manifest['digest'];
                $manifest = $this->getManifest($registry, $repository, $digest, $token);
            }

            $layers = $manifest['layers'] ?? [];
            $configSize = $manifest['config']['size'] ?? 0;
            $layersSize = collect($layers)->sum('size');
            $totalSize = $configSize + $layersSize;

            // 转换为 MB
            $totalSizeMB = round($totalSize / 1024 / 1024, 2);

            Log::info('镜像验证成功', [
                'image' => $image,
                'layers_count' => count($layers),
                'total_size_mb' => $totalSizeMB,
            ]);

            $result = [
                'valid' => true,
                'size_bytes' => $totalSize,
                'size_mb' => $totalSizeMB,
                'layers_count' => count($layers),
                'layers' => $layers,
            ];

            // 缓存 5 分钟
            Cache::put($cacheKey, $result, 300);

            return $result;

        } catch (\Exception $e) {
            Log::error('镜像验证失败', [
                'image' => $image,
                'error' => $e->getMessage(),
            ]);

            $result = [
                'valid' => false,
                'error' => $e->getMessage(),
            ];
            // 缓存错误结果 5 分钟，防止频繁请求
            Cache::put($cacheKey, $result, 300);

            return $result;
        }
    }

    /**
     * 解析镜像名称
     */
    private function parseImageName(string $image): array
    {
        // 添加默认标签
        if (! Str::contains($image, ':')) {
            $image .= ':latest';
        }

        // 检查是否包含注册表地址
        if (Str::contains($image, '/') && ! Str::startsWith($image, 'library/')) {
            // 包含注册表或组织名
            $parts = explode('/', $image, 2);

            // 判断第一部分是否为注册表地址（包含域名特征）
            if (Str::contains($parts[0], '.') || Str::contains($parts[0], ':')) {
                // 自定义注册表
                $registry = $parts[0];
                [$repository, $tag] = explode(':', $parts[1], 2);
                $repository = $parts[1];
                [$repository, $tag] = explode(':', $repository, 2);
            } else {
                // Docker Hub 组织镜像
                $registry = 'registry-1.docker.io';
                [$repository, $tag] = explode(':', $image, 2);
            }
        } else {
            // Docker Hub 官方镜像
            $registry = 'registry-1.docker.io';
            if (Str::contains($image, '/')) {
                [$repository, $tag] = explode(':', $image, 2);
            } else {
                $repository = 'library/'.explode(':', $image)[0];
                $tag = explode(':', $image)[1] ?? 'latest';
            }
        }

        return [$registry, $repository, $tag];
    }

    /**
     * 获取注册表认证凭据
     */
    private function getRegistryCredentials(string $registry, array $imagePullSecrets): ?array
    {
        if (empty($imagePullSecrets)) {
            return null;
        }

        try {
            // 获取 Kubernetes Secret
            $secretService = new SecretService($this->workspace);

            foreach ($imagePullSecrets as $secretName) {
                // 获取 Secret 的原始数据
                $secretData = $secretService->getSecretData($secretName);

                if (isset($secretData['.dockerconfigjson'])) {
                    $dockerConfig = json_decode($secretData['.dockerconfigjson'], true);

                    // 查找匹配的注册表凭据
                    $auths = $dockerConfig['auths'] ?? [];

                    foreach ($auths as $registryUrl => $auth) {
                        if (Str::contains($registryUrl, $registry) || $registryUrl === $registry) {
                            if (isset($auth['auth'])) {
                                [$username, $password] = explode(':', base64_decode($auth['auth']), 2);

                                return ['username' => $username, 'password' => $password];
                            }
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            Log::warning('获取镜像拉取凭据失败', [
                'registry' => $registry,
                'secrets' => $imagePullSecrets,
                'error' => $e->getMessage(),
            ]);
        }

        return null;
    }

    /**
     * 获取认证令牌
     */
    private function getAuthToken(string $registry, string $repository, ?array $credentials): string
    {
        if ($registry === 'registry-1.docker.io') {
            // Docker Hub
            $authUrl = "https://auth.docker.io/token?service=registry.docker.io&scope=repository:{$repository}:pull";
        } else {
            // 其他注册表，尝试通过 /v2/ 端点获取认证信息
            $authUrl = "https://{$registry}/v2/";
        }

        $timeout = config('k8s.imageValidation.timeout_seconds', 30);
        $options = ['timeout' => $timeout];

        if ($credentials) {
            $options['auth'] = [$credentials['username'], $credentials['password']];
        }

        try {
            $response = $this->httpClient->get($authUrl, $options);

            if ($registry === 'registry-1.docker.io') {
                $data = json_decode($response->getBody(), true);

                return $data['token'] ?? '';
            } else {
                // 对于其他注册表，如果直接访问成功，可能不需要 token
                return '';
            }
        } catch (RequestException $e) {
            if ($e->getResponse() && $e->getResponse()->getStatusCode() === 401) {
                // 处理 WWW-Authenticate 头
                $wwwAuth = $e->getResponse()->getHeader('WWW-Authenticate')[0] ?? '';

                if (Str::contains($wwwAuth, 'Bearer realm=')) {
                    // 解析 Bearer 认证信息
                    preg_match('/realm="([^"]+)"/', $wwwAuth, $realmMatches);
                    preg_match('/service="([^"]+)"/', $wwwAuth, $serviceMatches);

                    $realm = $realmMatches[1] ?? '';
                    $service = $serviceMatches[1] ?? '';

                    if ($realm && $service) {
                        $tokenUrl = "{$realm}?service={$service}&scope=repository:{$repository}:pull";
                        $tokenResponse = $this->httpClient->get($tokenUrl, $options);
                        $tokenData = json_decode($tokenResponse->getBody(), true);

                        return $tokenData['token'] ?? '';
                    }
                }
            }

            throw $e;
        }
    }

    /**
     * 获取镜像清单
     */
    private function getManifest(string $registry, string $repository, string $tagOrDigest, string $token): array
    {
        $manifestUrl = "https://{$registry}/v2/{$repository}/manifests/{$tagOrDigest}";

        $headers = [
            'Accept' => 'application/vnd.docker.distribution.manifest.v2+json, application/vnd.docker.distribution.manifest.list.v2+json',
        ];

        if ($token) {
            $headers['Authorization'] = "Bearer {$token}";
        }

        $timeout = config('k8s.imageValidation.timeout_seconds', 30);
        $options = [
            'headers' => $headers,
            'timeout' => $timeout,
        ];

        $response = $this->httpClient->get($manifestUrl, $options);

        return json_decode($response->getBody(), true);
    }

    /**
     * 创建 HTTP 客户端
     */
    private function createHttpClient(): Client
    {
        $timeout = config('k8s.imageValidation.timeout_seconds', 30);
        $options = [
            'timeout' => $timeout,
            'verify' => false, // 在生产环境中应该设置为 true
        ];

        // 配置代理
        if (config('app.proxy.enabled')) {
            $options['proxy'] = [
                'http' => config('app.proxy.http'),
                'https' => config('app.proxy.https'),
            ];
        }

        return new Client($options);
    }
}
