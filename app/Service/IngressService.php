<?php

namespace App\Service;

use App\ClusterLabel;
use App\DTOs\IngressDTO;
use App\Exceptions\Domain\DomainConflictException;
use App\Exceptions\Ingress\IngressNotFoundException;
use App\Exceptions\K8s\K8sConnectionException;
use App\Exceptions\K8s\ResourceConflictException;
use App\Exceptions\K8s\ResourceValidationException;
use App\Models\Ingress;
use App\Models\Workspace;
use Exception;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class IngressService
{
    protected IngressClassManager $ingressManager;

    protected TlsCertificateService $tlsService;

    public function __construct(
        protected Workspace $workspace
    ) {
        $this->ingressManager = new IngressClassManager;
        $this->tlsService = new TlsCertificateService($workspace);
    }

    /**
     * 获取 Ingress 列表
     */
    public function getIngresses(): array
    {
        try {
            $response = $this->workspace->cluster->http()
                ->get("/apis/networking.k8s.io/v1/namespaces/{$this->workspace->namespace}/ingresses");

            $items = $response->json('items', []);

            return array_map(function ($item) {
                return IngressDTO::fromK8sResource($item);
            }, $items);
        } catch (RequestException $e) {
            Log::error('获取 Ingress 列表失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'error' => $e->getMessage(),
            ]);

            throw new K8sConnectionException('无法获取 Ingress 列表', $e);
        } catch (\Exception $e) {
            Log::error('获取 Ingress 列表失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('获取 Ingress 列表失败：'.$e->getMessage());
        }
    }

    /**
     * 获取单个 Ingress
     */
    public function getIngress(string $name): IngressDTO
    {
        try {
            $response = $this->workspace->cluster->http()
                ->get("/apis/networking.k8s.io/v1/namespaces/{$this->workspace->namespace}/ingresses/{$name}");

            return IngressDTO::fromK8sResource($response->json());
        } catch (RequestException $e) {
            if ($e->response && $e->response->status() === 404) {
                Log::warning('Ingress 不存在', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'ingress_name' => $name,
                ]);

                throw new IngressNotFoundException($name, $this->workspace->namespace);
            }

            Log::error('获取 Ingress 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'ingress_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new K8sConnectionException('无法获取 Ingress 信息', $e);
        } catch (\Exception $e) {
            Log::error('获取 Ingress 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'ingress_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('获取 Ingress 失败：'.$e->getMessage());
        }
    }

    /**
     * 创建 Ingress
     */
    public function createIngress(array $data): IngressDTO
    {
        return DB::transaction(function () use ($data) {
            // 1. 验证 Ingress 类型
            $ingressClass = $data['ingress_class'] ?? 'traefik';
            if (! $this->ingressManager->hasDriver($ingressClass)) {
                throw new ResourceValidationException("不支持的 Ingress 类型：{$ingressClass}");
            }

            // 2. 验证配置
            $spec = $this->ingressManager->buildIngressSpec($ingressClass, $data);
            $validationErrors = $this->ingressManager->validateIngressSpec($ingressClass, $spec);
            if (! empty($validationErrors)) {
                throw new ResourceValidationException('配置验证失败', $validationErrors);
            }

            // 3. 创建数据库记录
            $ingress = Ingress::create([
                'workspace_id' => $this->workspace->id,
                'name' => $data['name'],
                'ingress_class' => $ingressClass,
                'domains' => $data['rules'] ?? [],
                'tls_config' => $data['tls'] ?? null,
                'status' => Ingress::STATUS_PENDING,
            ]);

            try {
                // 4. 检查域名冲突
                $conflicts = $ingress->checkDomainConflicts($data['rules'] ?? []);
                if (! empty($conflicts)) {
                    throw new DomainConflictException($conflicts);
                }

                // 5. 处理 TLS 证书自动生成
                $data = $this->processAutoTls($data);

                // 6. 构建 K8s Ingress 配置
                $payload = $this->buildIngressPayload($ingress, $data);

                // 7. 创建 K8s Ingress
                $response = $this->workspace->cluster->http()
                    ->post("/apis/networking.k8s.io/v1/namespaces/{$this->workspace->namespace}/ingresses", $payload);

                // 8. 更新状态为成功
                $ingress->update([
                    'status' => Ingress::STATUS_ACTIVE,
                    'status_message' => null,
                ]);

                Log::info('Ingress 创建成功', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'ingress_name' => $ingress->name,
                    'ingress_class' => $ingress->ingress_class,
                    'domains' => $ingress->getAllDomains(),
                ]);

                return IngressDTO::fromK8sResource($response->json());

            } catch (RequestException $e) {
                // 创建失败，更新状态
                $ingress->update([
                    'status' => Ingress::STATUS_FAILED,
                    'status_message' => $e->getMessage(),
                ]);

                if ($e->response && $e->response->status() === 409) {
                    Log::warning('Ingress 名称冲突', [
                        'workspace_id' => $this->workspace->id,
                        'namespace' => $this->workspace->namespace,
                        'ingress_name' => $ingress->name,
                    ]);

                    throw new ResourceConflictException("Ingress '{$ingress->name}' 已存在");
                }

                Log::error('Ingress 创建失败', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'ingress_name' => $ingress->name,
                    'error' => $e->getMessage(),
                ]);

                throw new K8sConnectionException('无法创建 Ingress', $e);
            } catch (\Exception $e) {
                // 9. 创建失败，更新状态
                $ingress->update([
                    'status' => Ingress::STATUS_FAILED,
                    'status_message' => $e->getMessage(),
                ]);

                Log::error('Ingress 创建失败', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'ingress_name' => $ingress->name,
                    'ingress_class' => $ingress->ingress_class,
                    'error' => $e->getMessage(),
                ]);

                throw $e;
            }
        });
    }

    /**
     * 更新 Ingress
     */
    public function updateIngress(string $name, array $data): IngressDTO
    {
        return DB::transaction(function () use ($name, $data) {
            // 先尝试从 K8s 获取当前 Ingress 以确保它存在
            try {
                $currentK8sIngress = $this->workspace->cluster->http()
                    ->get("/apis/networking.k8s.io/v1/namespaces/{$this->workspace->namespace}/ingresses/{$name}")
                    ->json();
            } catch (RequestException $e) {
                if ($e->response && $e->response->status() === 404) {
                    throw new IngressNotFoundException($name, $this->workspace->namespace);
                }

                throw new K8sConnectionException('无法获取 Ingress 信息', $e);
            } catch (Exception $e) {
                throw new Exception('获取 Ingress 信息失败: '.$e->getMessage());
            }

            // 尝试从数据库获取 Ingress 记录，如果不存在则创建一个临时记录
            $ingress = Ingress::where('workspace_id', $this->workspace->id)
                ->where('name', $name)
                ->first();

            if (! $ingress) {
                // 从 K8s 数据创建临时 Ingress 记录用于处理
                $ingress = new Ingress([
                    'workspace_id' => $this->workspace->id,
                    'name' => $name,
                    'ingress_class' => 'traefik',
                    'domains' => $currentK8sIngress['spec']['rules'] ?? [],
                    'tls_config' => $currentK8sIngress['spec']['tls'] ?? null,
                    'status' => Ingress::STATUS_ACTIVE,
                ]);
                // 设置关联关系但不保存到数据库
                $ingress->setRelation('workspace', $this->workspace);
            }

            // 1. 验证 Ingress 类型
            $ingressClass = $ingress->ingress_class;
            if (! $this->ingressManager->hasDriver($ingressClass)) {
                throw new Exception("不支持的 Ingress 类型：{$ingressClass}");
            }

            // 2. 验证配置
            $spec = $this->ingressManager->buildIngressSpec($ingressClass, $data);
            $validationErrors = $this->ingressManager->validateIngressSpec($ingressClass, $spec);
            if (! empty($validationErrors)) {
                throw new Exception('配置验证失败：'.implode('; ', $validationErrors));
            }

            try {
                // 3. 检查域名冲突（排除当前 Ingress）
                $tempIngress = clone $ingress;
                $tempIngress->domains = $data['rules'] ?? [];
                $conflicts = $tempIngress->checkDomainConflicts($data['rules'] ?? []);
                if (! empty($conflicts)) {
                    $messages = array_column($conflicts, 'message');
                    throw new Exception('域名冲突：'.implode('; ', $messages));
                }

                // 4. 处理 TLS 证书自动生成
                $data = $this->processAutoTls($data);

                // 5. 构建 K8s Ingress 配置
                $payload = $this->buildIngressPayload($ingress, $data);

                // 6. 更新 K8s Ingress
                $response = $this->workspace->cluster->http()
                    ->put("/apis/networking.k8s.io/v1/namespaces/{$this->workspace->namespace}/ingresses/{$name}", $payload);

                // 7. 更新或创建数据库记录
                if ($ingress->exists) {
                    $ingress->update([
                        'ingress_class' => $ingressClass,
                        'domains' => $data['rules'] ?? [],
                        'tls_config' => $data['tls'] ?? null,
                        'status' => Ingress::STATUS_ACTIVE,
                        'status_message' => null,
                    ]);
                } else {
                    // 如果不存在数据库记录，创建一个新的
                    $ingress->fill([
                        'ingress_class' => $ingressClass,
                        'domains' => $data['rules'] ?? [],
                        'tls_config' => $data['tls'] ?? null,
                        'status' => Ingress::STATUS_ACTIVE,
                        'status_message' => null,
                    ]);
                    $ingress->save();
                }

                Log::info('Ingress 更新成功', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'ingress_name' => $ingress->name,
                    'ingress_class' => $ingress->ingress_class,
                    'domains' => $ingress->getAllDomains(),
                ]);

                return IngressDTO::fromK8sResource($response->json());

            } catch (Exception $e) {
                // 更新失败，记录错误状态
                if ($ingress->exists) {
                    $ingress->update([
                        'status' => Ingress::STATUS_FAILED,
                        'status_message' => $e->getMessage(),
                    ]);
                }

                Log::error('Ingress 更新失败', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'ingress_name' => $name,
                    'error' => $e->getMessage(),
                ]);

                throw $e;
            }
        });
    }

    /**
     * 删除 Ingress
     */
    public function deleteIngress(string $name): void
    {
        DB::transaction(function () use ($name) {
            $ingress = Ingress::where('workspace_id', $this->workspace->id)
                ->where('name', $name)
                ->first();

            try {
                // 1. 删除 K8s Ingress
                $this->workspace->cluster->http()
                    ->delete("/apis/networking.k8s.io/v1/namespaces/{$this->workspace->namespace}/ingresses/{$name}");

                // 2. 删除数据库记录
                if ($ingress) {
                    $ingress->delete();
                }

                Log::info('Ingress 删除成功', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'ingress_name' => $name,
                ]);

            } catch (RequestException $e) {
                if ($e->response && $e->response->status() === 404) {
                    Log::warning('Ingress 不存在', [
                        'workspace_id' => $this->workspace->id,
                        'namespace' => $this->workspace->namespace,
                        'ingress_name' => $name,
                    ]);

                    throw new IngressNotFoundException($name, $this->workspace->namespace);
                }

                Log::error('Ingress 删除失败', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'ingress_name' => $name,
                    'error' => $e->getMessage(),
                ]);

                throw new K8sConnectionException('无法删除 Ingress', $e);
            } catch (Exception $e) {
                Log::error('Ingress 删除失败', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'ingress_name' => $name,
                    'error' => $e->getMessage(),
                ]);

                throw new K8sConnectionException('删除 Ingress 失败：'.$e->getMessage());
            }
        });
    }

    /**
     * 构建 K8s Ingress 配置
     */
    private function buildIngressPayload(Ingress $ingress, array $data): array
    {
        $baseMetadata = [
            'name' => $ingress->name,
            'namespace' => $this->workspace->namespace,
            'labels' => array_merge(
                $this->workspace->buildDefaultLabels(null, $ingress->name),
                [
                    ClusterLabel::WORKLOAD_TYPE->value => ClusterLabel::WORKLOAD_TYPE_INGRESS->value,
                    ClusterLabel::WORKLOAD_NAME->value => $ingress->name,
                ],
                $data['labels'] ?? []
            ),
        ];

        $metadata = $this->ingressManager->buildIngressMetadata($ingress->ingress_class, $baseMetadata);
        $spec = $this->ingressManager->buildIngressSpec($ingress->ingress_class, $data);

        return [
            'apiVersion' => 'networking.k8s.io/v1',
            'kind' => 'Ingress',
            'metadata' => $metadata,
            'spec' => $spec,
        ];
    }

    /**
     * 获取可用的 Ingress 类型
     */
    public function getAvailableIngressClasses(): array
    {
        return $this->ingressManager->getAvailableDrivers();
    }

    /**
     * 获取工作空间的 Ingress 列表（从数据库）
     */
    public function getWorkspaceIngresses(): array
    {
        return Ingress::where('workspace_id', $this->workspace->id)
            ->orderBy('created_at', 'desc')
            ->get()
            ->toArray();
    }

    /**
     * 检查域名可用性
     */
    public function checkDomainAvailability(array $domains): array
    {
        $tempIngress = new Ingress([
            'workspace_id' => $this->workspace->id,
        ]);
        $tempIngress->workspace = $this->workspace;

        return $tempIngress->checkDomainConflicts($domains);
    }

    /**
     * 获取集群域名使用情况
     */
    public function getClusterDomainUsage(): array
    {
        $ingresses = Ingress::whereHas('workspace', function ($query) {
            $query->where('cluster_id', $this->workspace->cluster_id);
        })->with('workspace')->get();

        $usage = [];
        foreach ($ingresses as $ingress) {
            foreach ($ingress->getAllDomains() as $domain) {
                if (! isset($usage[$domain])) {
                    $usage[$domain] = [];
                }
                $usage[$domain][] = [
                    'ingress_name' => $ingress->name,
                    'workspace_name' => $ingress->workspace->name,
                    'ingress_class' => $ingress->ingress_class,
                ];
            }
        }

        return $usage;
    }

    /**
     * 处理 TLS 证书自动生成
     */
    private function processAutoTls(array $data): array
    {
        // 仅当提交了 TLS 配置时才处理自动生成或校验证书
        if (! empty($data['tls'])) {
            foreach ($data['tls'] as &$tlsConfig) {
                if (isset($tlsConfig['hosts']) && ! empty($tlsConfig['hosts'])) {
                    // 如果没有指定 secretName 或者指定的是自动生成的名称格式
                    if (empty($tlsConfig['secretName']) || str_starts_with($tlsConfig['secretName'], 'auto-tls-')) {
                        try {
                            $secretName = $this->tlsService->generateAndCreateTlsSecret(
                                $tlsConfig['hosts'],
                                $tlsConfig['secretName'] ?? null
                            );
                            $tlsConfig['secretName'] = $secretName;

                            Log::info('为 TLS 配置自动生成证书', [
                                'workspace_id' => $this->workspace->id,
                                'hosts' => $tlsConfig['hosts'],
                                'secret_name' => $secretName,
                            ]);
                        } catch (Exception $e) {
                            Log::error('为 TLS 配置生成证书失败', [
                                'workspace_id' => $this->workspace->id,
                                'hosts' => $tlsConfig['hosts'],
                                'error' => $e->getMessage(),
                            ]);
                            // 如果自动生成失败，抛出异常阻止 Ingress 创建
                            throw new Exception('TLS 证书生成失败: '.$e->getMessage());
                        }
                    }
                    // 如果用户指定了 secretName，验证 Secret 是否存在
                    else {
                        try {
                            $response = $this->workspace->cluster->http()
                                ->get("/api/v1/namespaces/{$this->workspace->namespace}/secrets/{$tlsConfig['secretName']}");

                            if (! $response->successful()) {
                                throw new Exception("指定的 TLS Secret '{$tlsConfig['secretName']}' 不存在");
                            }

                            // 验证 Secret 类型
                            $secret = $response->json();
                            if (($secret['type'] ?? '') !== 'kubernetes.io/tls') {
                                throw new Exception("Secret '{$tlsConfig['secretName']}' 不是 TLS 类型");
                            }
                        } catch (Exception $e) {
                            Log::error('验证用户指定的 TLS Secret 失败', [
                                'workspace_id' => $this->workspace->id,
                                'secret_name' => $tlsConfig['secretName'],
                                'error' => $e->getMessage(),
                            ]);
                            throw new Exception('TLS Secret 验证失败: '.$e->getMessage());
                        }
                    }
                }
            }
        }

        return $data;
    }
}
