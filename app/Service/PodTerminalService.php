<?php

namespace App\Service;

use Amp\Http\Client\HttpClient;
use Amp\Http\Client\HttpClientBuilder;
use Amp\Socket\Certificate;
use Amp\Socket\ClientTlsContext;
use Amp\Socket\ConnectContext;
use Amp\Websocket\Client\Rfc6455Connector;
use Amp\Websocket\Client\WebsocketConnection;
use Amp\Websocket\Client\WebsocketHandshake;
use App\DTOs\PodCommandResultDTO;
use App\Models\Workspace;
use Illuminate\Support\Facades\Log;
use Workerman\Connection\AsyncTcpConnection;
use Workerman\Connection\TcpConnection;

class PodTerminalService
{
    protected JwtService $jwtService;

    public const MODE_SHELL = 'shell';

    public const MODE_ATTACH = 'attach';

    public const MODE_EXEC = 'exec';

    public const DEFAULT_COMMAND = ['sh', '-c', 'clear; (zsh || bash || ash || sh)'];

    // 最大输出大小限制 (1MB)
    public const MAX_OUTPUT_SIZE = 1024 * 1024;

    public function __construct(JwtService $jwtService)
    {
        $this->jwtService = $jwtService;
    }

    /**
     * 验证用户权限并建立 Pod 终端连接
     */
    public function connectToPod(TcpConnection $connection, array $tokenData): bool
    {
        try {
            // 验证 token 类型
            if (($tokenData['type'] ?? '') !== 'pod_terminal') {
                $this->sendError($connection, 'Invalid token type');

                return false;
            }

            // 获取必要参数
            $userId = $tokenData['user_id'] ?? null;
            $workspaceId = $tokenData['workspace_id'] ?? null;
            $podName = $tokenData['pod_name'] ?? null;
            $containerName = $tokenData['container_name'] ?? null;
            $mode = $tokenData['mode'] ?? 'shell';

            if (! $userId || ! $workspaceId || ! $podName) {
                $this->sendError($connection, 'Missing required parameters');

                return false;
            }

            // 验证工作空间权限
            $workspace = Workspace::find($workspaceId);
            if (! $workspace || $workspace->user_id !== $userId) {
                $this->sendError($connection, 'Access denied to workspace');

                return false;
            }

            // 验证 Pod 是否存在
            if (! $this->validatePodExists($workspace, $podName)) {
                $this->sendError($connection, "Pod '{$podName}' not found");

                return false;
            }

            // 如果没有指定容器，获取第一个容器
            if (! $containerName) {
                $containerName = $this->getDefaultContainer($workspace, $podName);
                if (! $containerName) {
                    $this->sendError($connection, 'No containers found in pod');

                    return false;
                }
            }

            // 建立到 Pod 的连接
            return $this->establishPodConnection($connection, $workspace, $podName, $containerName, $mode);

        } catch (\Exception $e) {
            Log::error('Pod terminal connection failed', [
                'error' => $e->getMessage(),
                'token_data' => $tokenData,
            ]);

            $this->sendError($connection, 'Connection failed: '.$e->getMessage());

            return false;
        }
    }

    /**
     * 建立到 Pod 的实际连接
     */
    protected function establishPodConnection(
        TcpConnection $connection,
        Workspace $workspace,
        string $podName,
        string $containerName,
        string $mode
    ): bool {
        try {
            $cluster = $workspace->cluster;

            // 构建 WebSocket 连接到 K8s API
            $wsUrl = $this->buildWebSocketUrl($cluster, $workspace->namespace, $podName, $containerName, $mode);

            // 保存连接信息到连接对象
            $connection->podInfo = [
                'workspace_id' => $workspace->id,
                'pod_name' => $podName,
                'container_name' => $containerName,
                'mode' => $mode,
            ];

            // 建立到 K8s 的 WebSocket 连接
            $this->createKubernetesConnection($connection, $wsUrl, $cluster);

            // 发送连接成功消息
            $this->sendMessage($connection, 'connected', [
                'pod_name' => $podName,
                'container_name' => $containerName,
                'mode' => $mode,
            ]);

            Log::info('Pod terminal connection established', [
                'workspace_id' => $workspace->id,
                'pod_name' => $podName,
                'container_name' => $containerName,
                'mode' => $mode,
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to establish pod connection', [
                'workspace_id' => $workspace->id,
                'pod_name' => $podName,
                'container_name' => $containerName,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 创建到 Kubernetes 的 WebSocket 连接
     */
    protected function createKubernetesConnection(TcpConnection $userConnection, string $wsUrl, $cluster): void
    {
        // 构建 SSL 上下文选项，参考 Cluster 模型的 http 方法
        $context = [];
        if ($cluster->insecure_skip_tls_verify) {
            $context['ssl'] = [
                'verify_peer' => false,
                'verify_peer_name' => false,
            ];
        } elseif ($cluster->certificate_authority_data) {
            $caFile = CertificateCacheService::getCachedCertificateFile('ca', $cluster->certificate_authority_data);
            $context['ssl'] = [
                'verify_peer' => true,
                'verify_peer_name' => true,
                'cafile' => $caFile,
            ];
        }

        // 处理客户端证书认证
        if ($cluster->auth_type === 'certificate') {
            if (empty($cluster->client_certificate_data) || empty($cluster->client_key_data)) {
                throw new \Exception('Certificate authentication requires certificate and key data');
            }
            // 对于 WebSocket，客户端证书需要在 SSL 上下文中设置
            $certFile = CertificateCacheService::getCachedCertificateFile('cert', $cluster->client_certificate_data);
            $keyFile = CertificateCacheService::getCachedCertificateFile('key', $cluster->client_key_data);
            if (! isset($context['ssl'])) {
                $context['ssl'] = [];
            }
            $context['ssl']['local_cert'] = $certFile;
            $context['ssl']['local_pk'] = $keyFile;
        }

        // 创建 AsyncTcpConnection

        // 将 wss:// 中的 wss 替换为 ws
        $wsUrl = str_replace('wss://', 'ws://', $wsUrl);
        $k8sConnection = new AsyncTcpConnection($wsUrl, $context);

        // 然后再设置 transport 为 ssl，可以解决 class \\Protocols\\Wss not exist 的问题。
        $k8sConnection->transport = 'ssl';

        // 设置请求头，参考 Cluster 模型的鉴权方式
        $headers = [
            'User-Agent' => 'PaaS-Terminal/1.0',
        ];

        switch ($cluster->auth_type) {
            case 'token':
                if (empty($cluster->token)) {
                    throw new \Exception('Token authentication requires a token');
                }
                $headers['Authorization'] = 'Bearer '.$cluster->token;
                break;

            case 'username_password':
                if (empty($cluster->username) || empty($cluster->password)) {
                    throw new \Exception('Username/password authentication requires username and password');
                }
                $headers['Authorization'] = 'Basic '.base64_encode($cluster->username.':'.$cluster->password);
                break;

            case 'certificate':
                // 证书认证已在 SSL 上下文中处理
                break;

            default:
                throw new \Exception("Unsupported authentication type: {$cluster->auth_type}");
        }

        $k8sConnection->headers = $headers;

        // TCP 连接成功后的回调
        $k8sConnection->onConnect = function ($k8sConnection) {
            Log::info('TCP connected to K8s API server');
        };

        // WebSocket 握手成功后的回调
        $k8sConnection->onWebSocketConnect = function ($k8sConnection, $response) use ($userConnection) {
            Log::info('K8s WebSocket handshake successful.');
            $userConnection->k8sConnection = $k8sConnection;

            // 客户端连接关闭时，关闭 K8s 连接
            $userConnection->onClose = function ($userConnection) use ($k8sConnection) {
                if (isset($userConnection->k8sConnection)) {
                    Log::info('Client disconnected, closing K8s connection.');
                    $k8sConnection->close();
                }
            };
        };

        // 收到 K8s 消息的回调
        $k8sConnection->onMessage = function ($k8sConnection, $data) use ($userConnection) {
            $this->handleKubernetesOutput($userConnection, $data);
        };

        // K8s 连接关闭的回调
        $k8sConnection->onClose = function ($k8sConnection) use ($userConnection) {
            Log::info('K8s connection closed');
            if (isset($userConnection->k8sConnection)) {
                unset($userConnection->k8sConnection);
            }
            if ($userConnection->getStatus() !== 'closed') {
                $userConnection->close();
            }
        };

        // K8s 连接错误的回调
        $k8sConnection->onError = function ($k8sConnection, $code, $msg) use ($userConnection) {
            Log::error('K8s connection error', ['code' => $code, 'message' => $msg]);
            $this->sendError($userConnection, "Kubernetes connection error: {$msg}");
            $userConnection->close();
        };

        $k8sConnection->connect();
    }

    /**
     * 直接向 Pod 执行命令并获取结果
     */
    public function executeCommand(
        Workspace $workspace,
        string $podName,
        ?string $containerName,
        array $command,
        ?string $workingDir = null,
        ?array $env = null,
        int $timeout = 30
    ): PodCommandResultDTO {
        $startTime = microtime(true);

        try {
            // 验证 Pod 是否存在
            if (! $this->validatePodExists($workspace, $podName)) {
                throw new \Exception("Pod '{$podName}' not found");
            }

            // 如果没有指定容器，获取第一个容器
            if (! $containerName) {
                $containerName = $this->getDefaultContainer($workspace, $podName);
                if (! $containerName) {
                    throw new \Exception('No containers found in pod');
                }
            }

            $cluster = $workspace->cluster;

            // 构建 WebSocket URL
            $wsUrl = $this->buildWebSocketUrl(
                $cluster,
                $workspace->namespace,
                $podName,
                $containerName,
                self::MODE_EXEC,
                $command
            );

            // 创建同步执行结果收集器
            $result = [
                'stdout' => '',
                'stderr' => '',
                'exit_code' => null,
                'error' => null,
                'completed' => false,
                'timeout' => false,
            ];

            // 使用 amphp 执行命令
            $this->executeWithAmphp($wsUrl, $cluster, $result, $timeout);

            $executionTime = microtime(true) - $startTime;
            $result['execution_time'] = $executionTime;

            return PodCommandResultDTO::fromArray($result);

        } catch (\Exception $e) {
            $executionTime = microtime(true) - $startTime;

            Log::error('Pod command execution failed', [
                'workspace_id' => $workspace->id,
                'pod_name' => $podName,
                'container_name' => $containerName,
                'command' => $command,
                'error' => $e->getMessage(),
                'execution_time' => $executionTime,
            ]);

            return PodCommandResultDTO::fromArray([
                'stdout' => '',
                'stderr' => '',
                'exit_code' => null,
                'error' => $e->getMessage(),
                'completed' => false,
                'timeout' => false,
                'execution_time' => $executionTime,
            ]);
        }
    }

    /**
     * 直接向 Pod 执行命令并获取结果（支持实时输出）
     */
    public function executeCommandWithRealTimeOutput(
        Workspace $workspace,
        string $podName,
        ?string $containerName,
        array $command,
        ?callable $outputCallback = null,
        ?string $workingDir = null,
        ?array $env = null,
        int $timeout = 30
    ): PodCommandResultDTO {
        $startTime = microtime(true);

        try {
            // 验证 Pod 是否存在
            if (! $this->validatePodExists($workspace, $podName)) {
                throw new \Exception("Pod '{$podName}' not found");
            }

            // 如果没有指定容器，获取第一个容器
            if (! $containerName) {
                $containerName = $this->getDefaultContainer($workspace, $podName);
                if (! $containerName) {
                    throw new \Exception('No containers found in pod');
                }
            }

            $cluster = $workspace->cluster;

            // 构建 WebSocket URL
            $wsUrl = $this->buildWebSocketUrl(
                $cluster,
                $workspace->namespace,
                $podName,
                $containerName,
                self::MODE_EXEC,
                $command
            );

            // 创建同步执行结果收集器
            $result = [
                'stdout' => '',
                'stderr' => '',
                'exit_code' => null,
                'error' => null,
                'completed' => false,
                'timeout' => false,
            ];

            // 使用 amphp 执行命令（支持实时输出）
            $this->executeWithAmphpRealTime($wsUrl, $cluster, $result, $timeout, $outputCallback);

            $executionTime = microtime(true) - $startTime;
            $result['execution_time'] = $executionTime;

            return PodCommandResultDTO::fromArray($result);

        } catch (\Exception $e) {
            $executionTime = microtime(true) - $startTime;

            Log::error('Pod command execution failed', [
                'workspace_id' => $workspace->id,
                'pod_name' => $podName,
                'container_name' => $containerName,
                'command' => $command,
                'error' => $e->getMessage(),
                'execution_time' => $executionTime,
            ]);

            return PodCommandResultDTO::fromArray([
                'stdout' => '',
                'stderr' => '',
                'exit_code' => null,
                'error' => $e->getMessage(),
                'completed' => false,
                'timeout' => false,
                'execution_time' => $executionTime,
            ]);
        }
    }

    /**
     * 使用 amphp 执行命令
     */
    protected function executeWithAmphp(string $wsUrl, $cluster, array &$result, int $timeout): void
    {
        try {
            // 创建自定义的 HTTP 客户端（处理 TLS）
            $httpClient = $this->createHttpClient($cluster);

            // 创建自定义的 WebSocket connector
            $connector = new Rfc6455Connector(httpClient: $httpClient);

            // 创建 WebSocket 握手请求
            $handshake = new WebsocketHandshake($wsUrl);

            // 设置认证头
            $handshake = $this->setAuthHeaders($handshake, $cluster);

            // 连接到 WebSocket
            $connection = $connector->connect($handshake);

            // 处理消息和超时
            $this->handleWebSocketMessages($connection, $result, $timeout);

        } catch (\Exception $e) {
            Log::error('Amphp WebSocket connection failed', [
                'url' => $wsUrl,
                'error' => $e->getMessage(),
            ]);

            $result['error'] = 'WebSocket connection failed: '.$e->getMessage();
            $result['completed'] = true;
        }
    }

    /**
     * 使用 amphp 执行命令（支持实时输出）
     */
    protected function executeWithAmphpRealTime(string $wsUrl, $cluster, array &$result, int $timeout, ?callable $outputCallback = null): void
    {
        try {
            // 创建自定义的 HTTP 客户端（处理 TLS）
            $httpClient = $this->createHttpClient($cluster);

            // 创建自定义的 WebSocket connector
            $connector = new Rfc6455Connector(httpClient: $httpClient);

            // 创建 WebSocket 握手请求
            $handshake = new WebsocketHandshake($wsUrl);

            // 设置认证头
            $handshake = $this->setAuthHeaders($handshake, $cluster);

            // 连接到 WebSocket
            $connection = $connector->connect($handshake);

            // 处理消息和超时（支持实时输出）
            $this->handleWebSocketMessagesRealTime($connection, $result, $timeout, $outputCallback);

        } catch (\Exception $e) {
            Log::error('Amphp WebSocket connection failed', [
                'url' => $wsUrl,
                'error' => $e->getMessage(),
            ]);

            $result['error'] = 'WebSocket connection failed: '.$e->getMessage();
            $result['completed'] = true;
        }
    }

    /**
     * 创建 HTTP 客户端
     */
    protected function createHttpClient($cluster): HttpClient
    {
        // 创建连接上下文
        $connectContext = new ConnectContext;

        // 设置 TLS 选项
        if ($cluster->insecure_skip_tls_verify) {
            $tlsContext = (new ClientTlsContext)->withPeerVerification(false);
        } else {
            $tlsContext = new ClientTlsContext;

            if ($cluster->certificate_authority_data) {
                $caFile = CertificateCacheService::getCachedCertificateFile('ca', $cluster->certificate_authority_data);
                $tlsContext = $tlsContext->withCaFile($caFile);
            }
        }

        // 处理客户端证书认证
        if ($cluster->auth_type === 'certificate') {
            if (empty($cluster->client_certificate_data) || empty($cluster->client_key_data)) {
                throw new \Exception('Certificate authentication requires certificate and key data');
            }

            $certFile = CertificateCacheService::getCachedCertificateFile('cert', $cluster->client_certificate_data);
            $keyFile = CertificateCacheService::getCachedCertificateFile('key', $cluster->client_key_data);

            $certificate = new Certificate($certFile, $keyFile);
            $tlsContext = $tlsContext->withCertificate($certificate);
        }

        $connectContext = $connectContext->withTlsContext($tlsContext);

        // 创建 HTTP 客户端
        $builder = new HttpClientBuilder;
        $builder = $builder->usingPool(
            new \Amp\Http\Client\Connection\UnlimitedConnectionPool(
                new \Amp\Http\Client\Connection\DefaultConnectionFactory(connectContext: $connectContext)
            )
        );

        return $builder->build();
    }

    /**
     * 设置认证头
     */
    protected function setAuthHeaders(WebsocketHandshake $handshake, $cluster): WebsocketHandshake
    {
        $handshake = $handshake->withHeader('User-Agent', 'PaaS-Terminal/1.0');

        switch ($cluster->auth_type) {
            case 'token':
                if (empty($cluster->token)) {
                    throw new \Exception('Token authentication requires a token');
                }
                $handshake = $handshake->withHeader('Authorization', 'Bearer '.$cluster->token);
                break;

            case 'username_password':
                if (empty($cluster->username) || empty($cluster->password)) {
                    throw new \Exception('Username/password authentication requires username and password');
                }
                $handshake = $handshake->withHeader('Authorization', 'Basic '.base64_encode($cluster->username.':'.$cluster->password));
                break;

            case 'certificate':
                // 证书认证已在 TLS 上下文中处理
                break;

            default:
                throw new \Exception("Unsupported authentication type: {$cluster->auth_type}");
        }

        return $handshake;
    }

    /**
     * 处理 WebSocket 消息
     */
    protected function handleWebSocketMessages(WebsocketConnection $connection, array &$result, int $timeout): void
    {
        $startTime = time();

        try {
            while (true) {
                // 检查超时
                if ((time() - $startTime) >= $timeout) {
                    $result['timeout'] = true;
                    $result['error'] = 'Command execution timeout';
                    break;
                }

                // 接收消息（带超时）
                $message = $connection->receive();

                if ($message === null) {
                    // 连接关闭
                    $result['completed'] = true;
                    break;
                }

                $payload = $message->buffer();

                if (strlen($payload) > 0) {
                    // 处理消息，如果返回true表示应该终止连接
                    if ($this->processMessage($payload, $result)) {
                        Log::info('Command execution terminated due to output size limit');
                        break;
                    }
                }

                // 如果命令已完成，退出循环
                if ($result['completed'] ?? false) {
                    break;
                }
            }
        } catch (\Exception $e) {
            Log::error('WebSocket message handling error', [
                'error' => $e->getMessage(),
            ]);

            $result['error'] = 'Message handling error: '.$e->getMessage();
        } finally {
            $connection->close();
        }
    }

    /**
     * 处理接收到的消息
     *
     * @return bool 返回true表示应该终止连接，false表示继续
     */
    protected function processMessage(string $payload, array &$result): bool
    {
        if (strlen($payload) > 0) {
            $channel = $payload[0];
            $data = substr($payload, 1);

            switch ($channel) {
                case "\x01": // STDOUT
                    if ($this->appendOutputData($result, 'stdout', $data)) {
                        return true; // 达到大小限制，终止连接
                    }
                    break;
                case "\x02": // STDERR
                    if ($this->appendOutputData($result, 'stderr', $data)) {
                        return true; // 达到大小限制，终止连接
                    }
                    break;
                case "\x03": // Error/Exit Code
                    // 尝试解析退出码
                    $exitData = json_decode($data, true);
                    if (is_array($exitData) && isset($exitData['status'])) {
                        $result['exit_code'] = $exitData['status'];
                        $result['completed'] = true;
                    } else {
                        $result['error'] = 'API Error: '.$data;
                        $result['completed'] = true;
                    }
                    break;
            }
        }

        return false; // 继续处理
    }

    /**
     * 检测字符串是否包含二进制数据
     */
    protected function isBinaryData(string $data): bool
    {
        // 检查是否包含空字节
        if (strpos($data, "\0") !== false) {
            return true;
        }

        // 如果数据为空，不是二进制
        if (strlen($data) === 0) {
            return false;
        }

        // 首先检查是否为有效的UTF-8
        if (mb_check_encoding($data, 'UTF-8')) {
            // 如果是有效的UTF-8，进一步检查是否包含过多控制字符
            $controlCharCount = 0;
            $totalLength = mb_strlen($data, 'UTF-8');

            for ($i = 0; $i < $totalLength; $i++) {
                $char = mb_substr($data, $i, 1, 'UTF-8');
                $ord = mb_ord($char, 'UTF-8');

                // 检查控制字符（除了常见的换行、制表符、回车）
                if ($ord < 32 && ! in_array($ord, [9, 10, 13])) {
                    $controlCharCount++;
                }
            }

            // 如果控制字符超过10%，认为是二进制数据
            return $totalLength > 0 && ($controlCharCount / $totalLength) > 0.1;
        }

        // 如果不是有效的UTF-8，检查是否包含大量非打印字符
        $nonPrintableCount = 0;
        $totalLength = strlen($data);

        for ($i = 0; $i < $totalLength; $i++) {
            $char = ord($data[$i]);
            // 控制字符（除了常见的换行、制表符等）
            if ($char < 32 && ! in_array($char, [9, 10, 13])) {
                $nonPrintableCount++;
            }
            // 高位字符（可能是二进制数据）
            if ($char > 126 && $char < 160) {
                $nonPrintableCount++;
            }
        }

        // 如果非打印字符超过30%，认为是二进制数据
        return ($nonPrintableCount / $totalLength) > 0.3;
    }

    /**
     * 安全地追加输出数据，处理大小限制和二进制数据
     *
     * @return bool 返回true表示应该终止连接，false表示继续
     */
    protected function appendOutputData(array &$result, string $type, string $data): bool
    {
        // 检查当前输出大小
        $currentSize = strlen($result[$type] ?? '');
        $newDataSize = strlen($data);

        if ($currentSize + $newDataSize > self::MAX_OUTPUT_SIZE) {
            // 超出大小限制，立即终止
            $remainingSpace = self::MAX_OUTPUT_SIZE - $currentSize;
            if ($remainingSpace > 0) {
                $data = substr($data, 0, $remainingSpace);
                $result[$type] = ($result[$type] ?? '').$data;
            }

            $result['error'] = 'Output size limit exceeded (1MB). Command execution terminated.';
            $result['size_limit_exceeded'] = true;
            $result['completed'] = true;

            // 返回true表示应该终止连接
            return true;
        }

        // 检查是否为二进制数据
        if ($this->isBinaryData($data)) {
            // 标记为二进制数据
            $result['is_binary'] = true;
            // 对二进制数据进行base64编码
            $result[$type] = ($result[$type] ?? '').base64_encode($data);
        } else {
            // 普通文本数据
            try {
                // 尝试验证UTF-8编码
                if (! mb_check_encoding($data, 'UTF-8')) {
                    // 如果不是有效的UTF-8，也当作二进制处理
                    $result['is_binary'] = true;
                    $result[$type] = ($result[$type] ?? '').base64_encode($data);
                } else {
                    $result[$type] = ($result[$type] ?? '').$data;
                }
            } catch (\Exception $e) {
                // 如果处理过程中出现异常，当作二进制处理
                $result['is_binary'] = true;
                $result[$type] = ($result[$type] ?? '').base64_encode($data);
            }
        }

        // 返回false表示继续处理
        return false;
    }

    /**
     * 处理 WebSocket 消息（支持实时输出）
     */
    protected function handleWebSocketMessagesRealTime(WebsocketConnection $connection, array &$result, int $timeout, ?callable $outputCallback = null): void
    {
        $startTime = time();

        try {
            while (true) {
                // 检查超时
                if ((time() - $startTime) >= $timeout) {
                    $result['timeout'] = true;
                    $result['error'] = 'Command execution timeout';

                    // 通知回调函数超时
                    if ($outputCallback) {
                        $outputCallback('timeout', 'Command execution timeout');
                    }

                    break;
                }

                // 接收消息（带超时）
                $message = $connection->receive();

                if ($message === null) {
                    // 连接关闭
                    $result['completed'] = true;

                    // 通知回调函数命令完成
                    if ($outputCallback) {
                        $outputCallback('completed', 'Command execution completed');
                    }

                    break;
                }

                $payload = $message->buffer();

                if (strlen($payload) > 0) {
                    // 处理消息，如果返回true表示应该终止连接
                    if ($this->processMessageRealTime($payload, $result, $outputCallback)) {
                        Log::info('Command execution terminated due to output size limit (real-time)');
                        break;
                    }
                }

                // 如果命令已完成，退出循环
                if ($result['completed'] ?? false) {
                    break;
                }
            }
        } catch (\Exception $e) {
            Log::error('WebSocket message handling error', [
                'error' => $e->getMessage(),
            ]);

            $result['error'] = 'Message handling error: '.$e->getMessage();

            // 通知回调函数发生错误
            if ($outputCallback) {
                $outputCallback('error', 'Message handling error: '.$e->getMessage());
            }
        } finally {
            $connection->close();
        }
    }

    /**
     * 处理接收到的消息（支持实时输出）
     *
     * @return bool 返回true表示应该终止连接，false表示继续
     */
    protected function processMessageRealTime(string $payload, array &$result, ?callable $outputCallback = null): bool
    {
        if (strlen($payload) > 0) {
            $channel = $payload[0];
            $data = substr($payload, 1);

            switch ($channel) {
                case "\x01": // STDOUT
                    if ($this->appendOutputDataRealTime($result, 'stdout', $data, $outputCallback)) {
                        return true; // 达到大小限制，终止连接
                    }
                    break;

                case "\x02": // STDERR
                    if ($this->appendOutputDataRealTime($result, 'stderr', $data, $outputCallback)) {
                        return true; // 达到大小限制，终止连接
                    }
                    break;

                case "\x03": // Error/Exit Code
                    // 尝试解析退出码
                    $exitData = json_decode($data, true);
                    if (is_array($exitData) && isset($exitData['status'])) {
                        $result['exit_code'] = $exitData['status'];
                        $result['completed'] = true;

                        // 实时回调退出码
                        if ($outputCallback) {
                            $outputCallback('exit_code', $exitData['status']);
                        }
                    } else {
                        $result['error'] = 'API Error: '.$data;
                        $result['completed'] = true;

                        // 实时回调错误
                        if ($outputCallback) {
                            $outputCallback('error', 'API Error: '.$data);
                        }
                    }
                    break;
            }
        }

        return false; // 继续处理
    }

    /**
     * 安全地追加输出数据（支持实时输出），处理大小限制和二进制数据
     *
     * @return bool 返回true表示应该终止连接，false表示继续
     */
    protected function appendOutputDataRealTime(array &$result, string $type, string $data, ?callable $outputCallback = null): bool
    {
        // 检查当前输出大小
        $currentSize = strlen($result[$type] ?? '');
        $newDataSize = strlen($data);

        if ($currentSize + $newDataSize > self::MAX_OUTPUT_SIZE) {
            // 超出大小限制，立即终止
            $remainingSpace = self::MAX_OUTPUT_SIZE - $currentSize;
            if ($remainingSpace > 0) {
                $data = substr($data, 0, $remainingSpace);
                $result[$type] = ($result[$type] ?? '').$data;

                // 实时回调截断的数据
                if ($outputCallback) {
                    $outputCallback($type, $data);
                }
            }

            $result['error'] = 'Output size limit exceeded (1MB). Command execution terminated.';
            $result['size_limit_exceeded'] = true;
            $result['completed'] = true;

            // 实时回调错误信息
            if ($outputCallback) {
                $outputCallback('error', 'Output size limit exceeded (1MB). Command execution terminated.');
                $outputCallback('terminated', 'Command execution terminated due to size limit');
            }

            // 返回true表示应该终止连接
            return true;
        }

        // 检查是否为二进制数据
        if ($this->isBinaryData($data)) {
            // 标记为二进制数据
            $result['is_binary'] = true;
            // 对二进制数据进行base64编码
            $encodedData = base64_encode($data);
            $result[$type] = ($result[$type] ?? '').$encodedData;

            // 实时回调编码后的数据
            if ($outputCallback) {
                $outputCallback($type, $encodedData);
                $outputCallback('binary_detected', 'Binary data detected and base64 encoded');
            }
        } else {
            // 普通文本数据
            try {
                // 尝试验证UTF-8编码
                if (! mb_check_encoding($data, 'UTF-8')) {
                    // 如果不是有效的UTF-8，也当作二进制处理
                    $result['is_binary'] = true;
                    $encodedData = base64_encode($data);
                    $result[$type] = ($result[$type] ?? '').$encodedData;

                    // 实时回调编码后的数据
                    if ($outputCallback) {
                        $outputCallback($type, $encodedData);
                        $outputCallback('binary_detected', 'Invalid UTF-8 data detected and base64 encoded');
                    }
                } else {
                    $result[$type] = ($result[$type] ?? '').$data;

                    // 实时回调原始数据
                    if ($outputCallback) {
                        $outputCallback($type, $data);
                    }
                }
            } catch (\Exception $e) {
                // 如果处理过程中出现异常，当作二进制处理
                $result['is_binary'] = true;
                $encodedData = base64_encode($data);
                $result[$type] = ($result[$type] ?? '').$encodedData;

                // 实时回调编码后的数据
                if ($outputCallback) {
                    $outputCallback($type, $encodedData);
                    $outputCallback('binary_detected', 'Data processing error, base64 encoded');
                }
            }
        }

        // 返回false表示继续处理
        return false;
    }

    /**
     * 创建同步的 Kubernetes 连接用于命令执行 (已弃用，使用 amphp 替代)
     */
    protected function createSyncKubernetesConnection(string $wsUrl, $cluster, array &$result, int $timeout): void
    {
        // 这个方法已经被 executeWithAmphp 替代，保留是为了向后兼容
        throw new \Exception('This method has been deprecated. Use executeWithAmphp instead.');
    }

    /**
     * 构建到 K8s 的 WebSocket URL
     */
    protected function buildWebSocketUrl(
        $cluster,
        string $namespace,
        string $podName,
        string $containerName,
        string $mode,
        ?array $command = []
    ): string {
        $baseUrl = str_replace(['http://', 'https://'], ['ws://', 'wss://'], $cluster->server_url);

        if ($mode === self::MODE_ATTACH) {
            // attach 模式：连接到容器的主进程
            $path = "/api/v1/namespaces/{$namespace}/pods/{$podName}/attach";
            $params = http_build_query([
                'container' => $containerName,
                'stdin' => 'true',
                'stdout' => 'true',
                'stderr' => 'true',
                'tty' => 'true',
            ]);
        } elseif ($mode === self::MODE_SHELL) {
            // shell 模式：执行 shell 命令
            $path = "/api/v1/namespaces/{$namespace}/pods/{$podName}/exec";

            // 手动构建 command 参数，因为需要多个 command=value 格式
            $commandParams = [
                'container' => $containerName,
                'stdin' => 'true',
                'stdout' => 'true',
                'stderr' => 'true',
                'tty' => 'true',
            ];

            $params = http_build_query($commandParams);

            $commands = empty($command) ? self::DEFAULT_COMMAND : $command;
            foreach ($commands as $cmd) {
                $params .= '&command='.urlencode($cmd);
            }
        } elseif ($mode === self::MODE_EXEC) {
            // exec 模式：直接执行命令
            $path = "/api/v1/namespaces/{$namespace}/pods/{$podName}/exec";

            $commandParams = [
                'container' => $containerName,
                'stdin' => 'false',
                'stdout' => 'true',
                'stderr' => 'true',
                'tty' => 'false',
            ];

            $params = http_build_query($commandParams);

            // 添加命令参数
            if (! empty($command)) {
                foreach ($command as $cmd) {
                    $params .= '&command='.urlencode($cmd);
                }
            }
        }

        return "{$baseUrl}{$path}?{$params}";
    }

    /**
     * 验证 Pod 是否存在
     */
    protected function validatePodExists(Workspace $workspace, string $podName): bool
    {
        try {
            $response = $workspace->cluster->http()
                ->get("/api/v1/namespaces/{$workspace->namespace}/pods/{$podName}");

            return $response->successful();
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 获取 Pod 的默认容器
     */
    protected function getDefaultContainer(Workspace $workspace, string $podName): ?string
    {
        try {
            $response = $workspace->cluster->http()
                ->get("/api/v1/namespaces/{$workspace->namespace}/pods/{$podName}");

            if ($response->successful()) {
                $pod = $response->json();
                $containers = $pod['spec']['containers'] ?? [];

                if (! empty($containers)) {
                    return $containers[0]['name'];
                }
            }

            return null;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 处理来自客户端的输入
     */
    public function handleInput(TcpConnection $connection, string $input): void
    {
        if (! isset($connection->k8sConnection)) {
            Log::warning('Received input but no K8s connection available.', ['pod_info' => $connection->podInfo ?? []]);

            return;
        }

        // 将 base64 解码后的数据加上 channel byte 后转发到 K8s Pod
        $this->forwardToKubernetes($connection, $input);
    }

    /**
     * 将输入转发到 Kubernetes
     */
    protected function forwardToKubernetes(TcpConnection $connection, string $input): void
    {
        if (isset($connection->k8sConnection)) {
            // 添加 STDIN channel (0)
            $connection->k8sConnection->send("\x00".$input);
        }
    }

    /**
     * 处理来自 Kubernetes 的输出
     */
    public function handleKubernetesOutput(TcpConnection $connection, string $output): void
    {
        if (strlen($output) > 0) {
            $channel = $output[0];
            $data = substr($output, 1);

            switch ($channel) {
                case "\x01": // STDOUT
                    $this->sendMessage($connection, 'output', ['data' => $data]);
                    break;
                case "\x02": // STDERR
                    // 同样作为 output 发送，前端终端会显示
                    $this->sendMessage($connection, 'output', ['data' => $data]);
                    break;
                case "\x03": // Error/Exit Code
                    // 尝试解析退出码
                    $exitData = json_decode($data, true);
                    if (is_array($exitData) && isset($exitData['status'])) {
                        $this->sendMessage($connection, 'exit', ['code' => $exitData['status']]);
                    } else {
                        $this->sendError($connection, 'API Error: '.$data);
                    }
                    break;
            }
        }
    }

    /**
     * 发送消息给客户端
     */
    protected function sendMessage(TcpConnection $connection, string $type, array $data = []): void
    {
        $message = json_encode([
            'type' => $type,
            'data' => $data,
            'timestamp' => microtime(true),
        ]);

        $connection->send($message);
    }

    /**
     * 发送错误消息
     */
    protected function sendError(TcpConnection $connection, string $message): void
    {
        $this->sendMessage($connection, 'error', ['message' => $message]);
    }

    /**
     * 清理连接
     */
    public function cleanup(TcpConnection $connection): void
    {
        if (isset($connection->k8sConnection)) {
            // 清理 K8s 连接
            $connection->k8sConnection->close();
            unset($connection->k8sConnection);
        }

        if (isset($connection->podInfo)) {
            Log::info('Pod terminal connection closed', [
                'pod_info' => $connection->podInfo,
            ]);
            unset($connection->podInfo);
        }
    }
}
