<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Workspace;
use App\Models\WorkspaceInvitation;

class WorkspaceInvitationPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, WorkspaceInvitation $workspaceInvitation): bool
    {
        // 只有被邀请的用户或工作空间所有者/成员可以查看
        return $user->email === $workspaceInvitation->email ||
               $this->canManageWorkspace($user, $workspaceInvitation->workspace);
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user, Workspace $workspace): bool
    {
        return $this->canManageWorkspace($user, $workspace);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, WorkspaceInvitation $workspaceInvitation): bool
    {
        return $this->canManageWorkspace($user, $workspaceInvitation->workspace);
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, WorkspaceInvitation $workspaceInvitation): bool
    {
        return $this->canManageWorkspace($user, $workspaceInvitation->workspace);
    }

    /**
     * Determine whether the user can accept the invitation.
     */
    public function accept(User $user, WorkspaceInvitation $workspaceInvitation): bool
    {
        return $user->email === $workspaceInvitation->email;
    }

    /**
     * Determine whether the user can reject the invitation.
     */
    public function reject(User $user, WorkspaceInvitation $workspaceInvitation): bool
    {
        return $user->email === $workspaceInvitation->email;
    }

    /**
     * 检查用户是否可以管理工作空间（所有者或成员）
     */
    protected function canManageWorkspace(User $user, Workspace $workspace): bool
    {
        return $workspace->user_id === $user->id || $workspace->hasMember($user);
    }
}
